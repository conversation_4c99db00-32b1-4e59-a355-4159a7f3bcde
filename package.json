{"name": "kratex-fe-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "format": "prettier --write .", "test": "jest --bail", "test:coverage": "jest --coverage", "ci:check": "prettier . --check && tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@kratex-tradetech/kratex-ui-library": "1.5.1", "@sentry/nextjs": "^9.22.0", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "axios": "^1.9.0", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.513.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "sonner": "^2.0.5", "zod": "^3.25.58", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.5", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/aria-query": "^5.0.4", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5"}}