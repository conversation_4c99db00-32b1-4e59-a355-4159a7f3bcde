# 🧱 Kratex Web App

Kratex is an innovative platform designed to facilitate seamless interactions among various stakeholders in the construction supply chain, including Buyers, Epod users, Kretex, Suppliers, Agents, and Kretex Admin. The platform is engineered to streamline the procurement process for construction goods through advanced technological solutions.

## 🚀 Tech Stack

- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript (strict mode enabled)
- **Styling**: Tailwind CSS 4.0
- **State Management**: Zustand with persistence
- **Data Fetching**: TanStack Query (React Query)
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Custom Kratex UI Library
- **Testing**: Jest + React Testing Library
- **Linting**: ESLint + Prettier
- **Package Manager**: pnpm
- **Deployment**: Docker (standalone output)

## ⚙️ Project Setup

Follow these steps to set up the project locally:

```bash
# 1️⃣ Clone the repository
<NAME_EMAIL>:KrateX-Tradetech/kratex-fe-next.git

# 2️⃣ Navigate into the project directory
cd kratex-fe-next

# 3️⃣ Configure .npmrc for private UI library packages
echo "@kratex-tradetech:registry=https://npm.pkg.github.com/" > .npmrc
echo "//npm.pkg.github.com/:_authToken=YOUR_GITHUB_TOKEN_HERE" >> .npmrc

# 4️⃣ Install dependencies using pnpm
pnpm install

# 5️⃣ Create a local environment file & update variables as needed
cp .env.example .env.local  # Create from example if available
# Edit .env.local with your configuration

# 6️⃣ Run the development server
pnpm dev
```

### Prerequisites

- **Node.js**: 18.17+ or 20+
- **pnpm**: Latest version
- **GitHub Token**: Required for accessing private UI library packages

---

## 📁 Project Structure

```
kratex-fe-next/
├── public/                          # Static assets
│   ├── images/                      # Image assets
│   └── appConfig.json              # Application configuration
├── src/
│   ├── app/                        # Next.js App Router
│   │   ├── (auth)/                 # Auth route group
│   │   │   └── login-signup/       # Authentication pages
│   │   ├── (dashboard)/            # Dashboard route group
│   │   │   ├── buyer/              # Buyer dashboard
│   │   │   ├── supplier/           # Supplier dashboard
│   │   │   └── layout.tsx          # Dashboard layout
│   │   ├── (onboarding)/           # Onboarding route group
│   │   │   ├── buyer/              # Buyer onboarding flow
│   │   │   └── supplier/           # Supplier onboarding flow
│   │   ├── globals.css             # Global styles
│   │   ├── layout.tsx              # Root layout
│   │   └── page.tsx                # Home page
│   ├── components/                 # Reusable UI components
│   │   ├── auth/                   # Authentication components
│   │   ├── common/                 # Shared components
│   │   ├── dashboard/              # Dashboard-specific components
│   │   ├── homepage/               # Homepage components
│   │   └── onboarding/             # Onboarding flow components
│   ├── hooks/                      # Custom React hooks
│   │   └── useAppConfig.ts         # App configuration hook
│   ├── lib/                        # Core utilities and configurations
│   │   ├── api-client.ts           # Axios API client setup
│   │   ├── cookies.ts              # Cookie management utilities
│   │   ├── error-handler.ts        # Centralized error handling
│   │   └── query-keys.ts           # TanStack Query key management
│   ├── providers/                  # React context providers
│   │   └── query-provider.tsx      # TanStack Query provider
│   ├── services/                   # API service functions (organized by domain)
│   │   ├── auth/                   # Authentication services
│   │   │   ├── useExchangeToken.ts
│   │   │   ├── useGetUserInfo.ts
│   │   │   ├── useRequestOTP.ts
│   │   │   └── useVerifyOTP.ts
│   │   ├── buyer/                  # Buyer-specific services
│   │   │   ├── useCreateSourcingIntent.ts
│   │   │   └── useRegisterBuyer.ts
│   │   ├── forms/                  # Form-related services
│   │   │   └── useGetFormDefinition.ts
│   │   ├── geolocation/            # Location services
│   │   │   └── useGetJurisdiction.ts
│   │   └── supplier/               # Supplier-specific services
│   ├── store/                      # Zustand state management
│   │   ├── newBuyer.store.ts       # Buyer onboarding state
│   │   ├── useAppConfig.store.ts   # App configuration state
│   │   └── user.store.ts           # User authentication state
│   ├── types/                      # TypeScript type definitions
│   │   ├── api.ts                  # API response types
│   │   ├── authentication.types.ts # Auth-related types
│   │   ├── newBuyer.types.ts       # Buyer onboarding types
│   │   └── user.types.ts           # User-related types
│   └── utils/                      # Utility functions and helpers
│       ├── formio/                 # Form.io integration utilities
│       ├── zustand/                # Zustand store utilities
│       ├── withRoleAccess.tsx      # Role-based access control
│       └── withRoleAccessBeforeLogin.tsx
├── .eslintrc.mjs                   # ESLint configuration
├── .prettierrc                     # Prettier configuration
├── commitlint.config.js            # Commit message linting
├── jest.config.ts                  # Jest testing configuration
├── lint-staged.config.js           # Pre-commit hooks
├── next.config.ts                  # Next.js configuration
├── package.json                    # Dependencies and scripts
├── tailwind.config.ts              # Tailwind CSS configuration
└── tsconfig.json                   # TypeScript configuration
```

---

## � Coding Standards

### Import Statement Order

All import statements must follow this strict sequence:

```typescript
// ✅ Correct Order Example
// 1. React and Next.js core
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

// 2. Third-party libraries
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';

// 3. UI Library components
import { Button, Avatar } from '@kratex-tradetech/kratex-ui-library';

// 4. Internal components
import { LoginForm } from '@/components/auth/LoginForm';

// 5. Services (organized by domain)
import { useExchangeToken } from '@/services/auth/useExchangeToken';
import { useRegisterBuyer } from '@/services/buyer/useRegisterBuyer';

// 6. Stores
import { userStore } from '@/store/user.store';

// 7. Types
import { UserTypeLabel } from '@/types/user.types';

// 8. Utilities and libs
import { setTokenInCookie } from '@/lib/cookies';
import { handleApiError } from '@/lib/error-handler';

// 9. Styles (if any)
import './component.css';
```

### Code Quality Rules

- **TypeScript Strict Mode**: Always enabled, no `any` types allowed (warn level)
- **Prefer `const`**: Use `const` over `let` unless reassignment is necessary
- **No Inline Styles**: Use Tailwind CSS classes or CSS modules
- **Error Handling**: All API calls must use centralized error handling
- **Type Safety**: Export and import types explicitly, use generics where appropriate

### Linting & Formatting Configuration

```javascript
// ESLint Rules (eslint.config.mjs)
rules: {
  '@typescript-eslint/no-explicit-any': 'warn',
}

// Prettier Configuration (.prettierrc)
{
  "semi": true,
  "singleQuote": true,
  "trailingComma": "all",
  "printWidth": 100,
  "tabWidth": 2
}
```

---

## 🧩 Naming Conventions

### 1. **Components**

- **Files**: PascalCase (e.g., `LoginForm.tsx`, `BuyerDashboard.tsx`)
- **Folders**: PascalCase for component-specific folders
- **Default Exports**: Use `index.tsx` for main component in folder

```
components/
├── auth/
│   ├── LoginForm.tsx
│   └── login-signup/
│       ├── Login-Signup.tsx
│       └── useLoginSignup.ts
├── dashboard/
│   ├── buyer/
│   │   ├── BuyerDashboard.tsx
│   │   └── useBuyerDashboard.ts
```

### 2. **Pages (App Router)**

- **Route Groups**: Use parentheses `(auth)`, `(dashboard)`, `(onboarding)`
- **Folders**: kebab-case for route segments
- **Files**: `page.tsx` for pages, `layout.tsx` for layouts

```
app/
├── (auth)/
│   └── login-signup/
│       └── page.tsx
├── (dashboard)/
│   ├── buyer/
│   │   └── page.tsx
│   └── layout.tsx
├── (onboarding)/
│   └── buyer/
│       ├── jurisdiction/
│       └── account-basics/
```

### 3. **Services (Domain-Organized)**

- **Files**: `use` prefix + PascalCase + descriptive action (e.g., `useExchangeToken.ts`)
- **Functions**: camelCase (e.g., `exchangeToken`, `registerBuyer`)
- **Folders**: Organized by domain (auth, buyer, supplier, forms, geolocation)

```
services/
├── auth/
│   ├── useExchangeToken.ts
│   ├── useRequestOTP.ts
│   └── useVerifyOTP.ts
├── buyer/
│   ├── useRegisterBuyer.ts
│   └── useCreateSourcingIntent.ts
├── forms/
│   └── useGetFormDefinition.ts
```

### 4. **Types**

- **Interfaces/Types**: PascalCase (e.g., `UserProfile`, `BuyerRegistrationPayload`)
- **Files**: Entity name + `.types.ts` suffix
- **Enums**: PascalCase with descriptive names

```
types/
├── user.types.ts
├── authentication.types.ts
├── newBuyer.types.ts
└── api.ts

// Inside user.types.ts
export interface UserProfile {
  id: number;
  email: string;
  phone: string;
}

export enum UserTypeLabel {
  BUYER = 2,
  SUPPLIER = 3,
}
```

### 5. **Stores (Zustand)**

- **Files**: Entity name + `.store.ts` suffix
- **Store Names**: camelCase + "Store" suffix
- **Actions**: Descriptive camelCase verbs

```
store/
├── user.store.ts
├── newBuyer.store.ts
└── useAppConfig.store.ts

// Inside user.store.ts
export const userStore = create<UserStore>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
}));
```

### 6. **Custom Hooks**

- **Files**: `use` prefix + PascalCase + descriptive name
- **Functions**: Same as file name
- **Location**: `/hooks/` for general hooks, co-located for component-specific

```
hooks/
├── useAppConfig.ts

// Component-specific hooks
components/auth/login-signup/useLoginSignup.ts
components/dashboard/buyer/useBuyerDashboard.ts
```

### 7. **Utilities & Libraries**

- **Files**: camelCase descriptive names
- **Functions**: camelCase
- **Folders**: kebab-case

```
lib/
├── api-client.ts
├── error-handler.ts
├── cookies.ts
└── query-keys.ts

utils/
├── formio/
├── zustand/
├── withRoleAccess.tsx
└── withRoleAccessBeforeLogin.tsx
```

---

## 🏗️ Architecture Patterns

### Service Layer Architecture

The project follows a domain-driven service architecture:

```typescript
// Services are organized by domain and follow React Query patterns
export const useExchangeToken = (): UseMutationResult<TokenResponse, Error, ExchangeInput> =>
  useMutation({
    mutationFn: ({ code, state }: ExchangeInput) => exchangeToken(code, state),
  });

// Each service handles its own error management
export async function exchangeToken(code: string, state: string): Promise<TokenResponse> {
  try {
    const response = await apiClient.get<TokenResponse>('/api/v1/users/token', {
      params: { code, state },
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to exchange token');
  }
}
```

### State Management Patterns

- **Zustand Stores**: Domain-specific stores with persistence
- **React Query**: Server state management with caching
- **Component State**: Local UI state only

```typescript
// Zustand store pattern
export const userStore = createPersistedStore<UserStore>('user-store', (set) => ({
  user: null,
  setUser: (user: User) => set({ user }),
  clearUser: () => set({ user: null }),
}));

// Query key management
export const queryKeys = {
  auth: {
    all: ['auth'] as const,
    me: () => [...queryKeys.auth.all, 'me'] as const,
  },
} as const;
```

### Component Patterns

- **Custom Hooks**: Business logic separated from UI components
- **Higher-Order Components**: Role-based access control
- **Co-location**: Related files grouped together

```typescript
// Component with custom hook pattern
const BuyerDashboard: React.FC = () => {
  const { user, handleLogout, isPopupOpen } = useBuyerDashboard();
  return (/* JSX */);
};

// Role-based access control
export default withBuyerRoute(BuyerDashboard);
```

---

### Git Workflow

1. **Branch Naming**: Use conventional prefixes
   - `feature/` - New features
   - `fix/` - Bug fixes
   - `refactor/` - Code refactoring
   - `docs/` - Documentation updates

2. **Commit Messages**: Follow conventional commits

   ```bash
   feat: add user authentication flow
   fix: resolve login redirect issue
   refactor: reorganize service layer architecture
   docs: update README with new patterns
   ```

3. **Pre-commit Hooks**: Automated quality checks
   ```bash
   # Runs automatically on commit
   - ESLint --fix
   - Prettier --write
   - TypeScript type checking
   ```

### Development Process

1. **Start Development Server**

   ```bash
   pnpm dev  # Runs with Turbopack for faster builds
   ```

2. **Code Quality Checks**

   ```bash
   pnpm lint          # ESLint checking
   pnpm format        # Prettier formatting
   pnpm ci:check      # Full CI checks (Prettier + TypeScript)
   ```

3. **Testing**

   ```bash
   pnpm test          # Run Jest tests
   pnpm test:coverage # Run tests with coverage report
   ```

4. **Build & Deploy**
   ```bash
   pnpm build         # Production build
   pnpm start         # Start production server
   ```

---

## 💡 Best Practices

### ✅ Code Quality

- **TypeScript Strict Mode**: Always enabled, no `any` types (warn level only)
- **Prefer `const`**: Use `const` over `let` unless reassignment is necessary
- **No Inline Styles**: Use Tailwind CSS classes exclusively
- **Error Handling**: All API calls use centralized `handleApiError`
- **Type Safety**: Export/import types explicitly, use generics appropriately

### 🧪 Testing Strategy

- **Framework**: Jest + React Testing Library
- **Coverage**: Test all services, utilities, and complex components
- **Mocking**: Mock external dependencies and API calls
- **Integration**: Test user workflows end-to-end

### 🔍 Code Review Guidelines

- **Import Order**: Verify correct import statement sequence
- **Naming**: Check adherence to naming conventions
- **Type Safety**: Ensure no `any` types without justification
- **Performance**: Review for unnecessary re-renders or API calls
- **Accessibility**: Verify proper ARIA labels and keyboard navigation

### 📦 Dependency Management

- **Package Manager**: Use `pnpm` exclusively
- **Lock Files**: Always commit `pnpm-lock.yaml`
- **Security**: Run `pnpm audit` regularly
- **Updates**: Keep dependencies updated, test thoroughly

### 🔐 Environment Configuration

- **Local Development**: Use `.env.local` for local overrides
- **Public Variables**: Prefix with `NEXT_PUBLIC_` for client-side access
- **Secrets**: Never commit sensitive data, use environment variables
- **Configuration**: Use `appConfig.json` for runtime configuration

---

## 📦 Available Scripts

| Script               | Description                             | Usage        |
| -------------------- | --------------------------------------- | ------------ |
| `pnpm dev`           | Start development server with Turbopack | Development  |
| `pnpm build`         | Create production build                 | Deployment   |
| `pnpm start`         | Start production server                 | Production   |
| `pnpm lint`          | Run ESLint checks                       | Code Quality |
| `pnpm format`        | Format code with Prettier               | Code Style   |
| `pnpm test`          | Run Jest test suite                     | Testing      |
| `pnpm test:coverage` | Run tests with coverage report          | Testing      |
| `pnpm ci:check`      | Run Prettier + TypeScript checks        | CI/CD        |

---

## 🔧 Configuration Files

| File                    | Purpose                  | Key Settings                                   |
| ----------------------- | ------------------------ | ---------------------------------------------- |
| `eslint.config.mjs`     | ESLint configuration     | Next.js + TypeScript + Prettier rules          |
| `.prettierrc`           | Code formatting          | Single quotes, trailing commas, 100 char width |
| `tsconfig.json`         | TypeScript configuration | Strict mode, path aliases (`@/*`)              |
| `commitlint.config.js`  | Commit message linting   | Conventional commits                           |
| `lint-staged.config.js` | Pre-commit hooks         | Auto-fix and format on commit                  |
| `next.config.ts`        | Next.js configuration    | Standalone output for Docker                   |
| `jest.config.ts`        | Testing configuration    | Jest + React Testing Library setup             |

---

## 🛠️ Contribution Guidelines

### Before Contributing

1. **Read Documentation**: Understand the architecture and patterns
2. **Check Issues**: Look for existing issues or create new ones
3. **Branch Strategy**: Create feature branches from `main`

### Development Standards

1. **Follow Conventions**: Adhere to all naming and coding standards
2. **Type Safety**: Maintain strict TypeScript compliance
3. **Test Coverage**: Write tests for new features and bug fixes
4. **Documentation**: Update README and code comments as needed

### Pull Request Process

1. **Quality Checks**: Ensure all linting and tests pass
2. **Code Review**: Request review from team members
3. **Commit Messages**: Use conventional commit format
4. **Documentation**: Update relevant documentation

### Commit Message Format

```bash
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

**Examples**:

```bash
feat(auth): add OAuth integration
fix(dashboard): resolve data loading issue
docs(readme): update setup instructions
refactor(services): reorganize API layer
```

---

## 📞 Support & Resources

- **Documentation**: This README and inline code comments
- **Issues**: GitHub Issues for bug reports and feature requests
- **Code Style**: ESLint + Prettier configurations
- **Commit Guidelines**: [Conventional Commits](https://www.conventionalcommits.org/)
- **Testing**: [Jest](https://jestjs.io/) + [React Testing Library](https://testing-library.com/)

---
