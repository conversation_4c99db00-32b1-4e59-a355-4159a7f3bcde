import {
  BuyerRegistrationPayload,
  BuyerRegistrationResponse,
} from '@/components/buyer/onboarding/account-basics/types';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function registerBuyer(payload: BuyerRegistrationPayload) {
  const response = await apiClient.post<BuyerRegistrationResponse>(
    '/api/v1/users/buyer/registration/',
    payload,
  );
  return response.data;
}

export const useBuyerRegister = ({
  onSettled,
  onError,
  onSuccess,
}: UseMutationHookProps<BuyerRegistrationResponse, unknown>) => {
  return useMutation<BuyerRegistrationResponse, AxiosError<unknown>, BuyerRegistrationPayload>({
    mutationFn: (payload) => registerBuyer(payload),
    onSettled,
    onError,
    onSuccess,
  });
};
