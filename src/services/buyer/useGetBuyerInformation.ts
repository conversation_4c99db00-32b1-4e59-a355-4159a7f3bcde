import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { BuyerInfoResponse } from '@/types/newBuyer.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function getBuyerInfo() {
  const response = await apiClient.get<BuyerInfoResponse>('/api/v1/users/buyer/info/');
  return response.data;
}

export const useGetBuyerInfo = ({
  onSettled,
  onError,
  onSuccess,
}: UseMutationHookProps<BuyerInfoResponse, unknown>) => {
  return useMutation<BuyerInfoResponse, AxiosError<unknown>>({
    mutationFn: () => getBuyerInfo(),
    onSettled,
    onError,
    onSuccess,
  });
};
