import { apiUrls } from '@/constants/apiUrls';
import axiosInstance from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

interface Payload {
  state: 'mark_user_kyc_skipped' | 'mark_invite_member_skipped';
}

const authSkip = async (payload: Payload) => {
  const res = await axiosInstance.patch(apiUrls.skip, payload);
  return res.data;
};

export const useSkip = ({ onError, onSuccess }: UseMutationHookProps<object, unknown>) => {
  return useMutation<object, AxiosError<unknown>, Payload>({
    mutationFn: (payload) => authSkip(payload),
    onError,
    onSuccess,
  });
};
