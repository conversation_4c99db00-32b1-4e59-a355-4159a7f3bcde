import { useMutation } from '@tanstack/react-query';
import axiosInstance from '@/lib/api-client';
import { apiUrls } from '@/constants/apiUrls';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import {
  VerifyAadhaarOtpPayload,
  VerifyAadhaarOtpResponse,
} from '@/types/sandboxVerification.types';

const verifyAadhaarOtp = async (payload: VerifyAadhaarOtpPayload) => {
  const response = await axiosInstance.post(apiUrls.aadhaar.verifyOtp, payload);
  return response.data;
};

export const usePostAadhaarOtpVerify = ({
  onSuccess,
  onError,
}: UseMutationHookProps<VerifyAadhaarOtpResponse, unknown>) => {
  return useMutation<VerifyAadhaarOtpResponse, AxiosError<unknown>, VerifyAadhaarOtpPayload>({
    mutationFn: (payload) => verifyAadhaarOtp(payload),
    onSuccess,
    onError,
  });
};
