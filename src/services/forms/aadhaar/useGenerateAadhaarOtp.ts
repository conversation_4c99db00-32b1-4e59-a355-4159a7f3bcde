import { useMutation } from '@tanstack/react-query';
import axiosInstance from '@/lib/api-client';
import { apiUrls } from '@/constants/apiUrls';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import { SendAadhaarPayload, SendAadhaarResponse } from '@/types/sandboxVerification.types';

const sendAadhaarOtp = async (payload: SendAadhaarPayload) => {
  const response = await axiosInstance.post(apiUrls.aadhaar.sendOtp, payload);
  return response.data;
};

export const usePostAadhaarOtp = ({
  onSuccess,
  onError,
}: UseMutationHookProps<SendAadhaarResponse, unknown>) => {
  return useMutation<SendAadhaarResponse, AxiosError<unknown>, SendAadhaarPayload>({
    mutationFn: (payload) => sendAadhaarOtp(payload),
    onSuccess,
    onError,
  });
};
