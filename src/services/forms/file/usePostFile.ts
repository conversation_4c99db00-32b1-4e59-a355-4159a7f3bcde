import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function uploadFile(payload: FormData) {
  const response = await apiClient.post(apiUrls.files.upload, payload);
  return response.data;
}

export const usePostFileUpload = ({
  onSuccess,
  onError,
}: UseMutationHookProps<{ id: string }, unknown>) => {
  return useMutation<{ id: string }, AxiosError<unknown>, FormData>({
    mutationFn: (payload) => uploadFile(payload),
    onError,
    onSuccess,
  });
};
