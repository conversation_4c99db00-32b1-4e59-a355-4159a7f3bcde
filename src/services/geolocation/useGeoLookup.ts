import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { GeoLookupResponse } from '@/types/geoLookUp.types';
import { handleApiError } from '@/lib/error-handler';

export const useGeoLookup = () => {
  return useQuery({
    queryKey: ['geo-lookup'],
    queryFn: getGeoLookup,
    staleTime: Infinity,
    retry: false,
    enabled: false,
  });
};

export async function getGeoLookup(): Promise<GeoLookupResponse> {
  try {
    const response = await axios.get('https://ipapi.co/json/');
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch geolocation info');
  }
}
