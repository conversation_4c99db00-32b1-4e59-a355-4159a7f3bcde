import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { UserTypeLabel } from '@/types/user.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function finalizeAndSubmit(userTypeLabel: UserTypeLabel) {
  const response = await apiClient.post<{ message: string }>(
    apiUrls.users.completeOnboarding(userTypeLabel),
  );
  return response.data;
}

export const useFinalizeOnboarding = ({
  onSettled,
  onError,
  onSuccess,
}: UseMutationHookProps<{ message: string }, unknown>) => {
  return useMutation<{ message: string }, AxiosError<unknown>, UserTypeLabel>({
    mutationFn: (userTypeLabel) => finalizeAndSubmit(userTypeLabel),
    onSettled,
    onError,
    onSuccess,
  });
};
