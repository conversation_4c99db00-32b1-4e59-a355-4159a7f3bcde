import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { UpdateUserRolePayload } from '@/types/user.types';
import { useMutation } from '@tanstack/react-query';

async function updateUserRole(payload: UpdateUserRolePayload) {
  const response = await apiClient.put(apiUrls.users.updateRole(), payload);
  return response.data!;
}

export const useUpdateUserRole = ({
  onError,
  onSuccess,
}: UseMutationHookProps<{ message: string }, unknown>) => {
  return useMutation({
    mutationFn: (payload: UpdateUserRolePayload) => updateUserRole(payload),
    onSuccess,
    onError,
  });
};
