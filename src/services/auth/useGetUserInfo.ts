import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { FinalizeInfoResponse } from '@/types/finalizeResponse.types';
import { UserTypeLabel } from '@/types/user.types';
import { useQuery } from '@tanstack/react-query';

async function getUserInfo(userTypeLabel: UserTypeLabel) {
  try {
    const response = await apiClient.get<FinalizeInfoResponse>(apiUrls.users.info(userTypeLabel));
    return response.data!;
  } catch (error) {
    throw handleApiError(error, `Failed to fetch ${userTypeLabel} info.`);
  }
}

export const useGetUserInfo = (userTypeLabel: UserTypeLabel) => {
  return useQuery({
    queryKey: [userTypeLabel, 'info'],
    queryFn: () => getUserInfo(userTypeLabel),
  });
};
