import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UserMeResponse } from '@/types/user.types';
import { useQuery } from '@tanstack/react-query';

const getMe = async () => {
  const response = await apiClient.get<UserMeResponse>(apiUrls.users.me());
  return response.data;
};

export const useGetMe = () => {
  return useQuery({
    queryKey: ['me'],
    queryFn: () => getMe(),
    staleTime: Infinity,
    enabled: false,
  });
};
