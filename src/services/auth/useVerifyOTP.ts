import apiClient from '@/lib/api-client';
import { OTPVerifyPayload, OTPVerifyResponse } from '@/types/otp.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

const verifyOTP = async (payload: OTPVerifyPayload): Promise<OTPVerifyResponse> => {
  const response = await apiClient.post('/api/v1/users/otp-verify', payload);
  return response.data;
};

export const useVerifyOTP = () =>
  useMutation<OTPVerifyResponse, AxiosError<unknown>, OTPVerifyPayload>({
    mutationFn: (payload) => verifyOTP(payload),
  });
