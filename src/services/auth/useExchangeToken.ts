import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import type { ExchangeInput } from '@/types/authentication.types';
import { TokenResponse } from '@/types/authentication.types';
import { useMutation, UseMutationResult } from '@tanstack/react-query';

async function exchangeToken(code: string, state: string): Promise<TokenResponse> {
  try {
    const response = await apiClient.get<TokenResponse>('/api/v1/users/token', {
      params: { code, state },
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to exchange token');
  }
}

export const useExchangeToken = (): UseMutationResult<TokenResponse, Error, ExchangeInput> =>
  useMutation({
    mutationFn: ({ code, state }: ExchangeInput) => exchangeToken(code, state),
  });
