import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { EmailOTPRequestPayload, EmailOTPResponse } from '@/types/email.otp.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function requestEmailOTP(payload: EmailOTPRequestPayload): Promise<EmailOTPResponse> {
  const response = await apiClient.post('/api/v1/users/email/otp', payload);
  return response.data;
}

export const useRequestEmailOTP = ({
  onError,
  onSuccess,
  onSettled,
}: UseMutationHookProps<EmailOTPResponse, { message: string }>) => {
  return useMutation<EmailOTPResponse, AxiosError<{ error: string }>, EmailOTPRequestPayload>({
    mutationFn: (payload) => requestEmailOTP(payload),
    onError: (err) => {
      const msg = err.response?.data?.error ?? err.message;
      onError?.({ ...err, message: msg } as AxiosError<{ message: string }>);
    },
    onSuccess,
    onSettled,
  });
};
