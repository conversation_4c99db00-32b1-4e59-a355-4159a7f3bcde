import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { useMutation } from '@tanstack/react-query';

async function deleteUser(id: number) {
  const response = await apiClient.delete(apiUrls.users.delete(id));
  return response.data!;
}

export const useDeleteUser = ({
  onError,
  onSuccess,
}: UseMutationHookProps<{ message: string }, unknown>) => {
  return useMutation({
    mutationFn: (id: number) => deleteUser(id),
    onSuccess,
    onError,
  });
};
