import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { OTPRequestPayload, OTPResponse } from '@/types/otp.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function requestOTP(payload: OTPRequestPayload): Promise<OTPResponse> {
  const response = await apiClient.post('/api/v1/users/otp', payload);
  return response.data;
}

export const useRequestOTP = ({
  onError,
  onSuccess,
  onSettled,
}: UseMutationHookProps<OTPResponse, { message: string }>) => {
  return useMutation<OTPResponse, AxiosError<{ message: string }>, OTPRequestPayload>({
    mutationFn: (payload) => requestOTP(payload),
    onError,
    onSuccess,
    onSettled,
  });
};
