import apiClient from '@/lib/api-client';
import { EmailOTPVerifyPayload, EmailOTPVerifyResponse } from '@/types/email.otp.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export const verifyEmailOTP = async (
  payload: EmailOTPVerifyPayload,
): Promise<EmailOTPVerifyResponse> => {
  const response = await apiClient.post('/api/v1/users/email/otp/verify', payload);
  return response.data;
};

export const useVerifyEmailOTP = () =>
  useMutation<EmailOTPVerifyResponse, AxiosError<unknown>, EmailOTPVerifyPayload>({
    mutationFn: (payload) => verifyEmailOTP(payload),
  });
