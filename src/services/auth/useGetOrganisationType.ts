import { OrgTypeParams } from '@/components/buyer/onboarding/org-type/type';
import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { PaginatedResponse } from '@/types/api';
import { OrgType } from '@/types/newBuyer.types';
import { queryKeys } from '@/lib/query-keys';
import { useQuery } from '@tanstack/react-query';

// Get all organisation types based on country and user_type
export async function getOrganisationTypes(
  country?: string,
  user_type?: string,
): Promise<OrgType[]> {
  try {
    const params: OrgTypeParams = {};
    if (country) params.country_iso2 = country;
    if (user_type) params.user_type = user_type;

    const response = await apiClient.get<PaginatedResponse<OrgType>>(
      '/api/v1/users/organisation-type/',
      { params },
    );
    return response.data?.results || [];
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch organisation types');
  }
}

// Get all organisation types
export const useGetOrganisationTypes = (country: string, user_type: string) => {
  return useQuery({
    queryKey: [...queryKeys.orgType.list(), country],
    queryFn: () => getOrganisationTypes(country, user_type),
    enabled: !!country,
  });
};
