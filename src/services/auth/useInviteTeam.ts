import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { useMutation } from '@tanstack/react-query';
import { InviteTeamPayload } from '@/types/inviteTeam.types';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import { apiUrls } from '@/constants/apiUrls';
import { UserTypeLabel } from '@/types/user.types';

export async function postInviteTeam(
  userTypeLabel: UserTypeLabel,
  payload: InviteTeamPayload,
): Promise<void> {
  try {
    await apiClient.post(apiUrls.users.invite(userTypeLabel), payload);
  } catch (error) {
    throw handleApiError(error, 'Failed to send invites');
  }
}

export const useInviteTeam = ({
  onSuccess,
  onError,
}: UseMutationHookProps<void, { message: string }>) => {
  return useMutation<
    void,
    AxiosError<{ message: string }>,
    { userTypeLabel: UserTypeLabel; payload: InviteTeamPayload }
  >({
    mutationFn: ({ userTypeLabel, payload }) => postInviteTeam(userTypeLabel, payload),
    onSuccess,
    onError,
  });
};
