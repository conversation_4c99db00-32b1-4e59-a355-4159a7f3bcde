import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

async function updateSourcingIntent(
  userTypeLabel: string,
  payload: SourcingIntentPayload,
): Promise<any> {
  try {
    const response = await apiClient.patch<any>(
      `/api/v1/users/${userTypeLabel}/sourcing-intent/update/`,
      payload,
    );
    return response.data!;
  } catch (error) {
    throw handleApiError(error, 'Failed to update sourcing intent.');
  }
}

export const useUpdateSourcingIntent = (userTypeLabel: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: SourcingIntentPayload) => updateSourcingIntent(userTypeLabel, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['buyer', 'sourcing-intent', 'info'],
      });
    },
  });
};
