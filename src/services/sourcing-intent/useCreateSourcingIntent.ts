import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function createSourcingIntent(payload: SourcingIntentPayload) {
  const response = await apiClient.post('/api/v1/users/buyer/sourcing-intent/create/', payload);
  return response;
}

export const useCreateSourcingIntent = ({
  onSuccess,
  onError,
}: UseMutationHookProps<unknown, { message: string }>) => {
  return useMutation<unknown, AxiosError<{ message: string }>, SourcingIntentPayload>({
    mutationFn: (payload) => createSourcingIntent(payload),
    onError,
    onSuccess,
  });
};
