import apiClient from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import { GstinLookupPayload, GstinLookupResponse } from '@/types/sandboxVerification.types';

async function lookupGSTN(payload: GstinLookupPayload) {
  const response = await apiClient.post<GstinLookupResponse>('/api/v1/kyc/gst/search', payload);
  return response.data;
}

export const useLookupGSTN = ({
  onSuccess,
  onError,
}: UseMutationHookProps<GstinLookupResponse, { message: string }>) => {
  return useMutation<GstinLookupResponse, AxiosError<{ message: string }>, GstinLookupPayload>({
    mutationFn: (payload) => lookupGSTN(payload),
    onSuccess,
    onError,
  });
};
