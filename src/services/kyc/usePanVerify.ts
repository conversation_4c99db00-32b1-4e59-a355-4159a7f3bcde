import apiClient from '@/lib/api-client';

import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import { PanVerifyPayload, PanVerifyResponse } from '@/types/sandboxVerification.types';
import { apiUrls } from '@/constants/apiUrls';

const lookupGSTN = async (payload: PanVerifyPayload) => {
  const response = await apiClient.post(apiUrls.panVerify, payload);
  return response.data;
};

export const usePanVerify = ({
  onSuccess,
  onError,
}: UseMutationHookProps<PanVerifyResponse, { message: string }>) => {
  return useMutation<PanVerifyResponse, AxiosError<{ message: string }>, PanVerifyPayload>({
    mutationFn: (payload) => lookupGSTN(payload),
    onSuccess,
    onError,
  });
};
