import { apiUrls } from '@/constants/apiUrls';
import axiosInstance from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { KYCPayload, KYCSuccessResponse } from '@/types/kyc.types';
import { UseMutationHookProps } from '@/types/api';

const submitKYCForm = async (payload: KYCPayload) => {
  const response = await axiosInstance.post<KYCSuccessResponse>(apiUrls.kyc, payload);
  return response.data;
};

export const useSubmitKYCForm = ({
  onSuccess,
  onError,
}: UseMutationHookProps<KYCSuccessResponse, unknown>) => {
  return useMutation({
    mutationFn: (payload: KYCPayload) => submitKYCForm(payload),
    onSuccess,
    onError,
  });
};
