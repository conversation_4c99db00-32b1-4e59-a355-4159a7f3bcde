{"keyword": "kycBuyerSoleProprietorIndia", "schema": {"title": "KYC - Buyer - SoleProprietor - India", "name": "kycBuyerSoleProprietorIndia", "path": "kycbuyersoleproprietorindia", "type": "form", "display": "form", "tags": [""], "components": [{"label": "Personal Info", "legend": "Personal Info", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "personalInfo", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "tableView": false, "components": [{"label": "Name", "labelPosition": "top", "placeholder": "Enter your full name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": false, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 2, "maxLength": 30, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "full_name", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "emngg1", "defaultValue": "", "dataGridLabel": false}, {"label": "Date of Birth", "labelPosition": "top", "displayInTimezone": "viewer", "useLocaleSettings": false, "allowInput": true, "format": "yyyy-MM-dd", "placeholder": "Select your date of birth", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "shortcutButtons": [], "enableDate": true, "datePicker": {"disable": "", "disableFunction": "", "disableWeekends": false, "disableWeekdays": false, "minDate": "", "maxDate": "", "showWeeks": true, "startingDay": 0, "initDate": "", "minMode": "day", "maxMode": "year", "yearRows": 4, "yearColumns": 5}, "enableTime": false, "timePicker": {"showMeridian": true, "hourStep": 1, "minuteStep": 1, "readonlyInput": false, "mousewheel": true, "arrowkeys": true}, "multiple": false, "defaultValue": "", "defaultDate": "", "customOptions": {}, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "enableMinDateInput": false, "enableMaxDateInput": false, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "dob", "tags": [], "properties": {}, "conditional": {"show": "", "when": "", "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "datetime", "input": true, "widget": {"type": "calendar", "displayInTimezone": "viewer", "locale": "en", "useLocaleSettings": false, "allowInput": true, "mode": "single", "enableTime": false, "noCalendar": false, "format": "yyyy-MM-dd", "hourIncrement": 1, "minuteIncrement": 1, "time_24hr": false, "minDate": "", "disabledDates": "", "disableWeekends": false, "disableWeekdays": false, "disableFunction": "", "maxDate": ""}, "prefix": "", "suffix": "", "refreshOn": "", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "datepickerMode": "day", "id": "e7xw3", "dataGridLabel": false, "timezone": ""}], "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "id": "ef6wkt", "dataGridLabel": false}, {"label": "Identity Verification", "legend": "Identity Verification", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "identityVerification", "tags": [], "properties": {}, "conditional": {"show": "", "when": "", "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "components": [{"label": "ID Type", "labelPosition": "top", "widget": "<PERSON><PERSON><PERSON>", "placeholder": "Select your ID document type", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "uniqueOptions": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "multiple": false, "dataSrc": "values", "data": {"values": [{"label": "<PERSON><PERSON><PERSON><PERSON> ", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Pan Card", "value": "pan"}, {"label": "Passport", "value": "passport"}, {"label": "Driving Licence", "value": "drivingLicence"}], "resource": "", "json": "", "url": "", "custom": ""}, "valueProperty": "", "dataType": "", "idPath": "id", "template": "<span>{{ item.label }}</span>", "refreshOn": "", "refreshOnBlur": "", "clearOnRefresh": false, "searchEnabled": true, "selectThreshold": 0.3, "readOnlyValue": false, "customOptions": {}, "useExactSearch": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "onlyAvailableItems": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "verificationDropdown", "tags": [], "properties": {"customRender": "true"}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "limit": 100, "type": "select", "input": true, "prefix": "", "suffix": "", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "fuseOptions": {"include": "score", "threshold": 0.3}, "indexeddb": {"filter": {}}, "id": "e1t0zc", "defaultValue": "", "redrawOn": "", "dataGridLabel": false, "authenticate": false, "ignoreCache": false, "lazyLoad": true, "filter": "", "searchDebounce": 0.3, "searchField": "", "minSearch": 0, "selectFields": ""}, {"label": "ID Document Upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "5MB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "idDocumentUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "useMultipartUpload": false, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "ee047l9", "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Selfie Upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "5MB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "selfieUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "egsv4fr", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Document Verification Status", "customClass": "", "modalEdit": false, "defaultValue": "false", "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "serverOverride": {}, "key": "documentVerificationStatus", "tags": [], "properties": {}, "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "hidden", "input": true, "tableView": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "unique": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "widget": {"type": "input"}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "hidden", "id": "eh8epu", "dataGridLabel": false}], "id": "egq25tw", "dataGridLabel": false}, {"label": "Business Details", "legend": "Business Details", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "businessDetails", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "components": [{"label": "Business Trade Name", "labelPosition": "top", "placeholder": "Enter your business trade name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 4, "maxLength": 30, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "businessTradeName", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eertv39", "defaultValue": "", "dataGridLabel": false}, {"label": "GST Certificate Upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "5MB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "gstCertificateUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "erbjmpe", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}], "id": "e372vd", "dataGridLabel": false}, {"label": "Residential Address", "legend": "Residential Address", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "residentialAddress", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "components": [{"label": "First Line", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "firstLine", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "<PERSON><PERSON><PERSON>", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "Second Line", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "secondLine", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "e67mokj", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "City", "labelPosition": "top", "placeholder": "City", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "city", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "egpbze8", "defaultValue": "", "dataGridLabel": false}, {"label": "State", "labelPosition": "top", "placeholder": "State", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "state", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "ekqpo2f", "defaultValue": "", "dataGridLabel": false}, {"label": "Country", "labelPosition": "top", "placeholder": "Country", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "country", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eeepvqd", "defaultValue": "", "dataGridLabel": false}, {"label": "Pin Code", "labelPosition": "top", "placeholder": "Pin code", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": false, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "pinCode", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "ewklew5", "defaultValue": "", "dataGridLabel": false}], "id": "e0d6le8", "dataGridLabel": false}], "machineName": "kycBuyerSoleProprietorIndia"}}