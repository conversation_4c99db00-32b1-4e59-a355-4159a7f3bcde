{"keyword": "kycBuyerGovernmentBodyIndia", "schema": {"_id": "6877936e74c00eb47a58b254", "title": "KYC - Buyer - Government Body - India", "name": "kycBuyerGovernmentBodyIndia", "path": "kycbuyergovernmentbodyindia", "type": "form", "display": "form", "tags": [""], "owner": "6864f6b374c00eb47a5894c1", "components": [{"label": "Department Name", "labelPosition": "top", "placeholder": "Enter Department Name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "aaaaaaaaaa", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "uppercase", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 10, "maxLength": 30, "minWords": 1, "maxWords": 1, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "Please enter a valid Department name.", "errors": "", "key": "departmentName", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eul0cme", "inputMaskPlaceholderChar": "", "defaultValue": null, "dataGridLabel": false}, {"label": "Tender Id", "labelPosition": "top", "placeholder": "Enter Tender Id", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "999999999999", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 12, "maxLength": 15, "strictDateValidation": false, "multiple": false, "unique": false, "minWords": 1, "maxWords": 1}, "unique": false, "validateWhenHidden": false, "errorLabel": "Please enter a valid Tender Id.", "errors": "", "key": "tenderId", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "enwr9y8", "defaultValue": "", "inputMaskPlaceholderChar": "", "dataGridLabel": false}, {"label": "Exemption Letter", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "50kb", "fileMaxSize": "1GB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "exemptionLetter", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "e95pa6c", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}], "pdfComponents": [], "access": [{"type": "read_all", "roles": ["6864f6b274c00eb47a589450", "6864f6b274c00eb47a58945d", "6864f6b274c00eb47a589468"]}], "submissionAccess": [], "created": "2025-07-16T11:56:30.985Z", "modified": "2025-07-23T14:40:26.492Z", "machineName": "kycGovernmentBodyVerificationIndiaTest"}}