{"keyword": "kycbuyerregisteredcompanyindia", "schema": {"title": "KYC - Buyer - Registered Company - India", "name": "kycBuyerRegisteredCompanyIndia", "path": "kycbuyerregisteredcompanyindia", "type": "form", "display": "form", "tags": [""], "owner": "6864f6b374c00eb47a5894c1", "components": [{"label": "Identification via GST", "legend": "Identification via GST", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "identificationViaGst", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "components": [{"label": "GST Number Verifcation Module", "customClass": "", "modalEdit": false, "defaultValue": "", "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "serverOverride": {}, "key": "gstnVerifcationModule", "tags": [], "properties": {"customRender": "true"}, "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "hidden", "input": true, "tableView": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "unique": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "widget": {"type": "input"}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "hidden", "id": "e5wars", "dataGridLabel": false}, {"label": "GST File Upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "1GB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "gstFileUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "ea41imo", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Legal Name", "labelPosition": "top", "placeholder": "Enter your legal name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 4, "maxLength": 30, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "legalName", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "egbnbe", "defaultValue": "", "dataGridLabel": false}, {"label": "Registered Address ", "legend": "Registered Address ", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "disabled": false, "modalEdit": false, "key": "registeredAddress", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "fieldset", "input": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "hideLabel": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "serverOverride": {}, "tree": false, "lazyLoad": false, "components": [{"label": "First Line", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 5, "maxLength": 20, "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "addressLine1", "tags": [], "properties": {}, "conditional": {"show": "", "when": "", "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "elkmras", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "Second Line", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "secondLine", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "efy0kje", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "City", "labelPosition": "top", "placeholder": "City", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "city", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "e5vfz5", "defaultValue": "", "dataGridLabel": false}, {"label": "State", "labelPosition": "top", "placeholder": "State", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "state", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eci8x3c", "defaultValue": "", "dataGridLabel": false}, {"label": "Country", "labelPosition": "top", "placeholder": "Country", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "country", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "epk0j5", "defaultValue": "", "dataGridLabel": false}, {"label": "Pin Code", "labelPosition": "top", "placeholder": "pin code", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "pinCode", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "ed07gl9", "defaultValue": "", "dataGridLabel": false}], "id": "e6y1ig", "dataGridLabel": false}, {"label": "Certificate Of Incorporation", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "1GB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "certificateOfIncorporation", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "e7k1mor", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Board Authorization File upload based on Constitution", "customClass": "", "modalEdit": false, "defaultValue": null, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "serverOverride": {}, "key": "boardAuthorizationFileUpload", "tags": [], "properties": {"customRender": "true"}, "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "hidden", "input": true, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "unique": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "widget": {"type": "input"}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "hidden", "id": "eglefod", "dataGridLabel": false}], "id": "e6yxk9s", "dataGridLabel": false}], "pdfComponents": [], "access": [{"type": "read_all", "roles": ["6864f6b274c00eb47a589450", "6864f6b274c00eb47a58945d", "6864f6b274c00eb47a589468"]}], "submissionAccess": [], "created": "2025-07-18T12:50:26.382Z", "modified": "2025-07-22T19:17:40.795Z", "machineName": "kycBuyerRegisteredCompanyIndia"}}