{"keyword": "Buying Agent India", "schema": {"title": "KYC - Buyer - Buying Agent - India", "name": "kycBuyerBuyingAgentIndia", "path": "kycbuyerbuyingagentindia", "type": "form", "display": "form", "tags": [""], "owner": "6864f6b374c00eb47a5894c1", "components": [{"label": "Agency Name", "labelPosition": "top", "placeholder": "Enter Agency Name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "agency_name", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "ei65b3h", "defaultValue": "", "dataGridLabel": false}, {"label": "Power Of Attorney", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "1GB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": true, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "powerOfAttorney", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "erse4b5", "useMultipartUpload": false, "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Principals Count", "labelPosition": "top", "widget": "<PERSON><PERSON><PERSON>", "placeholder": "Please select Principals Count", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "uniqueOptions": false, "autofocus": false, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "dataSrc": "values", "data": {"values": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": ">5", "value": ">5"}], "resource": "", "json": "", "url": "", "custom": ""}, "valueProperty": "", "dataType": "", "idPath": "id", "template": "<span>{{ item.label }}</span>", "refreshOn": "", "refreshOnBlur": "", "clearOnRefresh": false, "searchEnabled": true, "selectThreshold": 0.3, "readOnlyValue": false, "customOptions": {}, "useExactSearch": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "onlyAvailableItems": false, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "principalsCount", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "limit": 100, "type": "select", "input": true, "prefix": "", "suffix": "", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "fuseOptions": {"include": "score", "threshold": 0.3}, "indexeddb": {"filter": {}}, "id": "evw0k2m", "defaultValue": "", "redrawOn": "", "dataGridLabel": false, "authenticate": false, "ignoreCache": false, "lazyLoad": true, "filter": "", "searchDebounce": 0.3, "searchField": "", "minSearch": 0, "selectFields": ""}], "pdfComponents": [], "access": [{"type": "read_all", "roles": ["6864f6b274c00eb47a589450", "6864f6b274c00eb47a58945d", "6864f6b274c00eb47a589468"]}], "submissionAccess": [], "created": "2025-07-17T10:46:27.601Z", "modified": "2025-07-19T08:31:23.639Z", "machineName": "kycBuyerBuyingAgentIndia"}}