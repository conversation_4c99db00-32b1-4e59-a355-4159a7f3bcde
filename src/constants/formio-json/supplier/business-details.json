{"keyword": "businessDetailsSupplierIndia", "schema": {"title": "Business Details Supplier India", "name": "businessDetailsSupplierIndia", "path": "businessdetailssupplierindia", "type": "form", "display": "form", "components": [{"label": "Legal business name", "labelPosition": "top", "placeholder": "Enter your Legal Business Name", "description": "Exactly as your licence", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 3, "maxLength": 100, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "legal_business_name", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "e9a1afa", "defaultValue": "", "dataGridLabel": false}, {"label": "Company / GST / VAT number", "labelPosition": "top", "placeholder": "e.g., 27AAEPM0123C1Z5 (India GST)", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "registration_number", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "e7gd1mw", "defaultValue": "", "dataGridLabel": false}, {"label": "Address Line 1", "labelPosition": "top", "placeholder": "Enter address line 1", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "address_line1", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "eyo4j9", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "Address Line 2", "labelPosition": "top", "placeholder": "Enter address line 2", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "editor": "", "autoExpand": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "html", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": false, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "minWords": "", "maxWords": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "address_line2", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "rows": 3, "type": "textarea", "input": true, "refreshOn": "", "widget": {"type": "input"}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputMask": "", "fixedSize": true, "id": "ey5ade9", "defaultValue": "", "dataGridLabel": false, "wysiwyg": false}, {"label": "City", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "city", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eueol48", "defaultValue": "", "dataGridLabel": false}, {"label": "Postcode", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "postcode", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "enon8pk", "defaultValue": "", "dataGridLabel": false}, {"label": "Primary contact person", "labelPosition": "top", "placeholder": "Enter primary contact person name", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "primary_contact_name", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "eh6seu", "defaultValue": "", "dataGridLabel": false}, {"label": "Contact phone", "labelPosition": "top", "placeholder": "Enter contact phone", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": true, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "^[+]?[0-9]{10,15}$", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": 10, "maxLength": 15, "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "phone", "tags": [], "properties": {"key": "phone"}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "epx5hjn", "defaultValue": "", "dataGridLabel": false}, {"label": "Customer-support e-mail", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "displayMask": "", "applyMaskOn": "change", "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "kickbox": {"enabled": false}, "errorLabel": "", "errors": "", "key": "support_email", "tags": [], "properties": {}, "conditional": {"show": "", "when": "", "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "email", "input": true, "refreshOn": "", "widget": {"type": "input"}, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "email", "inputMask": "", "id": "e73j028", "defaultValue": "", "dataGridLabel": false}, {"label": "Contact phone with optional Whatsapp verification", "customClass": "", "modalEdit": false, "defaultValue": null, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "serverOverride": {}, "key": "phoneVerificationModule", "tags": [], "properties": {}, "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "hidden", "input": true, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "unique": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "tableView": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "widget": {"type": "input"}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "hidden", "id": "e2tzi65j", "dataGridLabel": false}]}}