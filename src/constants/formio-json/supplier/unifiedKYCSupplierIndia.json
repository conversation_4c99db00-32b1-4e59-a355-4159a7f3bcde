{"keyword": "supplierUnifiedKycIndia", "schema": {"title": "KYC - Supplier - India", "name": "supplierUnifiedKycIndia", "path": "supplierUnifiedKycIndia", "type": "form", "display": "form", "tags": [""], "components": [{"label": "Tax certificate", "labelPosition": "top", "placeholder": "Tax certificate number", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "taxCertificate", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "ebk2t2", "defaultValue": "", "dataGridLabel": false}, {"label": "Tax certificate upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "", "fileMaxSize": "", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": true, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "taxCertificateUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "useMultipartUpload": false, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "eqkdxgl", "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "Business License", "labelPosition": "top", "placeholder": "Business License number", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "inputMask": "", "displayMask": "", "applyMaskOn": "change", "allowMultipleMasks": false, "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "showWordCount": false, "showCharCount": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "businessLicense", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "textfield", "input": true, "refreshOn": "", "addons": [], "inputType": "text", "id": "enp4vhc", "defaultValue": "", "dataGridLabel": false}, {"label": "Business license upload", "labelPosition": "top", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "hidden": false, "hideLabel": false, "autofocus": false, "disabled": false, "tableView": false, "modalEdit": false, "storage": "s3", "fileNameTemplate": "", "image": false, "uploadOnly": false, "webcam": false, "capture": false, "fileTypes": [{"label": "", "value": ""}], "filePattern": "*", "fileMinSize": "0KB", "fileMaxSize": "1GB", "multiple": false, "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "serverOverride": {}, "validate": {"required": true, "customMessage": "", "custom": "", "customPrivate": false, "json": "", "strictDateValidation": false, "multiple": false, "unique": false}, "validateWhenHidden": false, "errorLabel": "", "errors": "", "key": "businessLicenseUpload", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "useMultipartUpload": false, "type": "file", "input": true, "placeholder": "", "prefix": "", "suffix": "", "defaultValue": null, "unique": false, "refreshOn": "", "widget": null, "validateOn": "change", "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "id": "ecev8n5", "dataGridLabel": false, "privateDownload": false, "imageSize": "200"}, {"label": "BrandproofModule", "customClass": "", "modalEdit": false, "defaultValue": "", "persistent": true, "protected": false, "dbIndex": false, "encrypted": false, "redrawOn": "", "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "serverOverride": {}, "key": "brandproofModule", "tags": [], "properties": {"customRender": "true"}, "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "hidden", "input": true, "tableView": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "unique": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "widget": {"type": "input"}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "hidden", "id": "enfkmsg", "dataGridLabel": false}], "submissionAccess": [], "created": "2025-07-28T08:22:07.734Z", "modified": "2025-07-28T09:26:01.176Z", "machineName": "supplierUnifiedKycIndia"}}