import { getKycPayload } from '@/services/kyc/useGetKycStatus';
import { UserTypeLabel } from '@/types/user.types';

const BASE = '/api/v1';
export const apiUrls = {
  files: {
    upload: `${BASE}/files/`,
  },
  aadhaar: {
    sendOtp: `${BASE}/kyc/aadhaar/otp`,
    verifyOtp: `${BASE}/kyc/aadhaar/otp/verify`,
  },
  kyc: `${BASE}/kyc/`,
  panVerify: `${BASE}/kyc/pan/verify`,
  skip: `${BASE}/users/states/`, // used to update backend state of user if he chooses to skip an step in onboarding
  users: {
    // keyword(formio kyc form schema's keyword) might be updated later to be set from backend itself
    getKycStatusData: ({ userTypeLabel, keyword }: getKycPayload) =>
      `${BASE}/users/${userTypeLabel}/kyc/${keyword}/`,
    info: (userTypeLabel: UserTypeLabel) => `${BASE}/users/${userTypeLabel}/info/`,
    me: () => `${BASE}/users/me`,
    invite: (userTypeLabel: UserTypeLabel) => `${BASE}/users/${userTypeLabel}/invite`,
    invitedTeamMembers: `${BASE}/users/invites`,
    completeOnboarding: (userTypeLabel: UserTypeLabel) =>
      `${BASE}/users/${userTypeLabel}/onboarding/complete/`,
    delete: (id: number) => `${BASE}/users/${id}`,
    updateRole: () => `${BASE}/users/role`,
  },
  supplier: {
    register: `${BASE}/users/supplier/registration/`,
  },
};
