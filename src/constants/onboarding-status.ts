export const ONBOARDING_STATES = {
  MO<PERSON>LE_VERIFIED: 'mobile_verified',
  MARK_ACCOUNT_BASICS_FILLED: 'mark_account_basics_filled',
  MARK_BUSINESS_DETAILS_FILLED: 'mark_business_details_filled',
  MARK_USER_KYC_SKIPPED: 'mark_user_kyc_skipped',
  MARK_USER_KYC_FILLED: 'mark_user_kyc_filled',
  MARK_SOURCING_INTENT_FILLED: 'mark_sourcing_intent_filled',
  MARK_MEMBER_INVITED: 'mark_member_invited',
  MARK_INVITE_MEMBER_SKIPPED: 'mark_invite_member_skipped',
  MARK_ONBOARDING_COMPLETED: 'mark_onboarding_completed',
} as const;

export type OnboardingState = (typeof ONBOARDING_STATES)[keyof typeof ONBOARDING_STATES];
