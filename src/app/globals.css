@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif;
  --font-mono: var(--font-geist-mono), ui-monospace, SFMono-Regular, monospace;
}
/* Temporarily commenting out, might be added back later
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif !important;
}

* {
  font-family: inherit;
}

/* Override any potential UI library font declarations */
h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
div,
button,
input,
textarea,
select {
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif !important;
}
