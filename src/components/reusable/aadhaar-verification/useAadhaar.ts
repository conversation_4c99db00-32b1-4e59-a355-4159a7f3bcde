import { usePostAadhaarOtp } from '@/services/forms/aadhaar/useGenerateAadhaarOtp';
import { usePostAadhaarOtpVerify } from '@/services/forms/aadhaar/useVerifyAadhaarOtp';
import { useState } from 'react';
import { toast } from 'sonner';
import { useCustomDataStore } from '@/store/customData.store';

export const useAadhaar = () => {
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [aadhaarNumber, setAadhaarNumber] = useState('');
  const { setSandboxResponse, clearAllSandboxResponse, setModuleData } = useCustomDataStore();

  const { mutate: sendAadhaarOtp, isSuccess: isOtpSentSuccess } = usePostAadhaarOtp({
    onSuccess: (data) => {
      if (data.status === 0) {
        toast.success(data.message || `OTP has been sent!`);
        setOtpSent(true);
      } else {
        toast.error(data.message || `Error sending OTP. Please try again later.`);
      }
    },
    onError: (error) => {
      toast.error(error.message || `Error sending OTP. Please try again later.`);
    },
  });

  const {
    mutate: verifyAadhaarOtp,
    isError: isVerifyError,
    isPending,
    isSuccess: isOtpVerified,
  } = usePostAadhaarOtpVerify({
    onSuccess: (data) => {
      clearAllSandboxResponse();
      if (data.status === 0) {
        setSandboxResponse({ aadhaarResponse: data });
        setModuleData({ aadhaar: aadhaarNumber });
        toast.success(data.message || `Aadhaar verified successfully!`);
      } else {
        toast.error(data.message || `Aadhaar verification failed. Please try again later.`);
      }
    },
    onError: (error) => {
      toast.error(error.message || `Incorrect OTP.`);
    },
  });

  const handleVerifyOtp = () => verifyAadhaarOtp({ otp });

  const handleOtpSend = () => sendAadhaarOtp({ aadhaar_number: aadhaarNumber });

  return {
    handleOtpSend,
    handleVerifyOtp,
    setAadhaarNumber,
    aadhaarNumber,
    otp,
    setOtp,
    isPending,
    otpSent,
    isVerifyError,
    isOtpSentSuccess,
    isOtpVerified,
  };
};
