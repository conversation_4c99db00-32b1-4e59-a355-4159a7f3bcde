import { HTMLAttributes, ReactNode } from 'react';

interface OnboardingFormWrapperProps extends HTMLAttributes<HTMLDivElement> {
  formTitle: string;
  formDescription: string;
  formTitleClassName?: string;
  formDescriptionClassName?: string;
  children: ReactNode;
}

const OnboardingFormWrapper = ({
  formTitle,
  formDescription,
  children,
  formTitleClassName,
  formDescriptionClassName,
  className,
  ...props
}: OnboardingFormWrapperProps) => {
  return (
    <div
      {...props}
      className={`flex min-h-screen flex-col items-center justify-center p-8 ${className}`}
    >
      <div className="mb-6 text-center">
        <h2 className={`mb-2 text-2xl font-semibold ${formTitleClassName}`}>{formTitle}</h2>
        <p className={`text-gray-600 ${formDescriptionClassName}`}>{formDescription}</p>
      </div>

      {children}
    </div>
  );
};

export default OnboardingFormWrapper;
