'use client';

import { useCustomDataStore } from '@/store/customData.store';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { useState } from 'react';

const DrivingLicenseVerification = () => {
  const { clearAllSandboxResponse, setModuleData } = useCustomDataStore();
  const [drivingLicence, setDrivingLicence] = useState('');

  const handleBlur = () => {
    clearAllSandboxResponse();
    setModuleData({ drivingLicence });
  };

  return (
    <>
      <FormItem className="space-x-2">
        <FormLabel>
          Driving Licence Number <span className="text-red-500">*</span>
        </FormLabel>
        <FormControl>
          <div className="relative">
            <Input
              value={drivingLicence}
              onChange={(e) => setDrivingLicence(e.target.value)}
              onBlur={handleBlur}
              placeholder="Enter your driving license number"
            />
          </div>
        </FormControl>
        <FormMessage />
      </FormItem>
    </>
  );
};

export default DrivingLicenseVerification;
