'use client';

import { useLookupGSTN } from '@/services/kyc/useLookupGSTN';
import { useCustomDataStore } from '@/store/customData.store';
import {
  Button,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle, LoaderCircle } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

const GSTNVerification = ({ form }: { form: UseFormReturn }) => {
  const { mutate, isPending, isSuccess } = useLookupGSTN({
    onSuccess: (data) => {
      clearAllSandboxResponse();
      // adding the response to pass to the backend as payload
      setSandboxResponse({ gstResponse: data });
      toast.success('GST number verified successfully!');
    },
    onError: handleError,
  });
  const { setSandboxResponse, clearAllSandboxResponse } = useCustomDataStore();

  const handleVerifyGST = () => {
    if (isPending) return;
    const gstin = form.getValues('gstin');
    mutate({ gstin });
  };

  function handleError() {
    toast.error('Failed to verify GST number!');
  }

  return (
    <>
      <FormField
        control={form.control}
        name={'gstin'}
        render={({ field }) => (
          <FormItem className="space-x-2">
            <FormLabel htmlFor={'GST Number'}>
              GST Number <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input placeholder="Enter your GST number" {...field} />
                {isSuccess && (
                  <CheckCircle className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 transform text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {!isSuccess && (
        <Button type="button" disabled={isPending} className="w-full" onClick={handleVerifyGST}>
          {isPending ? (
            <div className="flex items-center">
              Verifying <LoaderCircle className="ml-1 animate-spin" />
            </div>
          ) : (
            'Verify GST number'
          )}
        </Button>
      )}
    </>
  );
};

export default GSTNVerification;
