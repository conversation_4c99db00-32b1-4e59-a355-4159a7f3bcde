import { usePanVerify } from '@/services/kyc/usePanVerify';
import { useCustomDataStore } from '@/store/customData.store';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

const usePanVerification = (form: UseFormReturn) => {
  const [pan, setPan] = useState('');

  const { setSandboxResponse, clearAllSandboxResponse, setModuleData } = useCustomDataStore();
  const { mutate, isPending, data } = usePanVerify({
    onSuccess: (data) => {
      clearAllSandboxResponse();
      if (data.status === 0) {
        setSandboxResponse({ panVerification: data });
        setModuleData({ pan });
        toast.success(data.message);
      } else {
        toast.error(data.message || 'PAN verification failed. Please try again later.');
      }
    },
    onError: () => {
      toast.error('Unable to verify Pan. Please check and try again later!');
    },
  });

  const fullName = form.watch('full_name');
  const dobRaw = form.watch('dob');
  const dob = dobRaw
    ? new Date(dobRaw).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })
    : '';

  const isSubmitDisabled = isPending || !pan.trim() || !fullName.trim() || !dob.trim();

  const handleSubmit = () => {
    clearAllSandboxResponse();
    mutate({
      pan_number: pan,
      full_name: fullName,
      dob,
    });
  };

  return {
    isPending,
    handleSubmit,
    isSuccess: data?.status === 0,
    pan,
    setPan,
    isSubmitDisabled,
  };
};

export default usePanVerification;
