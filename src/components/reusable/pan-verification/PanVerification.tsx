'use client';

import {
  Button,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle, LoaderCircle } from 'lucide-react';
import usePanVerification from './usePanVerification';
import { UseFormReturn } from 'react-hook-form';

const PanVerification = ({ form }: { form: UseFormReturn }) => {
  const { isPending, isSuccess, handleSubmit, pan, setPan, isSubmitDisabled } =
    usePanVerification(form);

  return (
    <>
      <FormItem className="space-x-2">
        <FormLabel>
          PAN Number <span className="text-red-500">*</span>
        </FormLabel>
        <FormControl>
          <div className="relative">
            <Input
              value={pan}
              onChange={(e) => setPan(e.target.value)}
              placeholder="Enter your PAN number"
            />
            {isSuccess && (
              <CheckCircle className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 transform text-green-500" />
            )}
          </div>
        </FormControl>
        <FormMessage />
      </FormItem>

      <Button type="button" disabled={isSubmitDisabled} className="w-full" onClick={handleSubmit}>
        {isPending ? (
          <div className="flex items-center">
            Verifying <LoaderCircle className="ml-1 animate-spin" />
          </div>
        ) : (
          'Verify Pan number'
        )}
      </Button>
    </>
  );
};

export default PanVerification;
