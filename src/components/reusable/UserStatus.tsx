import { Badge } from '@kratex-tradetech/kratex-ui-library';

export type StatusType = 'invited' | 'active' | 'archived';

interface StatusBadgeProps {
  status: StatusType;
}

const statusMap: Record<
  StatusType,
  {
    label: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
    className?: string;
  }
> = {
  invited: {
    label: 'Invited',
    variant: 'secondary',
    className: 'text-blue-600 bg-blue-100 hover:bg-blue-200',
  },
  active: {
    label: 'Active',
    variant: 'default',
    className: 'bg-green-100 text-green-700 hover:bg-green-200',
  },
  archived: {
    label: 'Archived',
    variant: 'outline',
    className: 'text-gray-500 border-gray-300',
  },
};

export const UserStatus = ({ status }: StatusBadgeProps) => {
  const { label, variant, className } = statusMap[status];

  return (
    <Badge variant={variant} className={className}>
      {label}
    </Badge>
  );
};
