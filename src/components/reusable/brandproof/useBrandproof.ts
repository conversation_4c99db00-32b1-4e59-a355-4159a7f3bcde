import { useCustomDataStore } from '@/store/customData.store';
import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useFileIdsStore } from '@/store/fileIds.store';

export interface FileUploadItem {
  id: string;
  key: string;
  label: string;
}

const INITIAL_FILE_UPLOAD: FileUploadItem = {
  id: '1',
  key: 'brandProofFileUpload_1',
  label: 'Brand Proof Document',
};

const useBrandproof = (form: UseFormReturn<any>) => {
  const { setModuleData } = useCustomDataStore();
  const { fileIds, setFileId } = useFileIdsStore();

  const [fileUploads, setFileUploads] = useState<FileUploadItem[]>([INITIAL_FILE_UPLOAD]);
  const nextId = useRef<number>(2);

  const brandProofData = useMemo(() => {
    const data: Record<string, string> = {};
    fileUploads.forEach((_, index) => {
      const sequentialNumber = index + 1;
      data[`brandProofName_${sequentialNumber}`] =
        form.getValues(`brandProofName_${sequentialNumber}`) || '';
      data[`brandProofFileUpload_${sequentialNumber}`] =
        form.getValues(`brandProofFileUpload_${sequentialNumber}`) || '';
    });
    return data;
  }, [fileUploads, form]);

  useEffect(() => {
    setModuleData({ brandProofData });
  }, [brandProofData, setModuleData]);

  useEffect(() => {
    const sub = form.watch((_, { name }) => {
      if (name?.startsWith('brandProofFileUpload_') || name?.startsWith('brandProofName_')) {
        setFileUploads((prev) => [...prev]);
      }
    });
    return () => sub.unsubscribe();
  }, [form]);

  const reorganizeFormDataAndFileIds = useCallback(
    (removedIndex: number) => {
      const allValues = form.getValues();
      const newFileIds: Record<string, string> = {};

      // Loop through all file uploads to unregister old fields and re-register remaining ones with sequential numbering
      // Items before removedIndex keep same sequence, items after get decremented by 1
      fileUploads.forEach((_, idx) => {
        const oldSeq = idx + 1;
        const oldNameKey = `brandProofName_${oldSeq}`;
        const oldFileKey = `brandProofFileUpload_${oldSeq}`;

        form.unregister(oldNameKey);
        form.unregister(oldFileKey);

        if (idx !== removedIndex) {
          const newSeq = idx < removedIndex ? oldSeq : oldSeq - 1;
          const newNameKey = `brandProofName_${newSeq}`;
          const newFileKey = `brandProofFileUpload_${newSeq}`;

          form.setValue(newNameKey, allValues[oldNameKey] || '');
          form.setValue(newFileKey, allValues[oldFileKey] || '');

          if (fileIds?.[oldFileKey]) {
            newFileIds[newFileKey] = fileIds[oldFileKey];
          }
        }
      });

      // Preserve other non-brandProof file IDs and merge with new brandProof IDs
      if (fileIds) {
        const updatedFileIds = { ...fileIds };

        // Remove old brandProof entries
        Object.keys(updatedFileIds).forEach((key) => {
          if (key.startsWith('brandProofFileUpload_')) {
            delete updatedFileIds[key];
          }
        });

        // Add new brandProof entries
        Object.assign(updatedFileIds, newFileIds);

        setFileId(updatedFileIds);
      }
    },
    [fileUploads, form, fileIds, setFileId],
  );

  const addMoreFiles = useCallback(() => {
    const idNum = nextId.current++;
    const newItem: FileUploadItem = {
      id: String(idNum),
      key: `brandProofFileUpload_${fileUploads.length + 1}`,
      label: `Brand Proof Document`,
    };
    setFileUploads((prev) => [...prev, newItem]);
  }, [fileUploads.length]);

  const removeFileUpload = useCallback(
    (id: string) => {
      if (fileUploads.length <= 1) return;

      const removedIndex = fileUploads.findIndex((item) => item.id === id);
      if (removedIndex === -1) return;

      reorganizeFormDataAndFileIds(removedIndex);

      setFileUploads((prev) => prev.filter((item) => item.id !== id));
    },
    [fileUploads, reorganizeFormDataAndFileIds],
  );

  const handleBlur = useCallback(() => {
    setModuleData({ brandProofData });
  }, [brandProofData, setModuleData]);

  const reset = useCallback(() => {
    form.reset();
    setFileUploads([INITIAL_FILE_UPLOAD]);
    nextId.current = 2;
  }, [form]);

  return {
    fileUploads,
    addMoreFiles,
    removeFileUpload,
    reset,
    handleBlur,
    brandProofData,
  };
};

export default useBrandproof;
