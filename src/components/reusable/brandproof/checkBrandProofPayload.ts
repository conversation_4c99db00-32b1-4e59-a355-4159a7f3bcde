import { KYCPayload } from '@/types/kyc.types';

export const checkPayloadForBrandProof = (payload: KYCPayload) => {
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  const submissionData = payload.submission_data as Record<string, unknown> | undefined;

  if (!submissionData) {
    return {
      isValid: false,
      error: 'Submission data is missing.',
    };
  }

  const brandProofFileFields = Object.keys(submissionData).filter((key) =>
    key.startsWith('brandProofFileUpload_'),
  );

  if (brandProofFileFields.length === 0) {
    return {
      isValid: false,
      error: 'At least one brand proof file upload is required.',
    };
  }

  for (const fileField of brandProofFileFields) {
    const fieldId = fileField.replace('brandProofFileUpload_', '');
    const nameField = `brandProofName_${fieldId}`;

    const brandProofName = submissionData[nameField] as string;
    if (!brandProofName || brandProofName.trim() === '') {
      return {
        isValid: false,
        error: `Brand proof name is required for document ${fieldId}.`,
      };
    }

    const fileId = submissionData[fileField] as string;
    if (!fileId || fileId.trim() === '') {
      return {
        isValid: false,
        error: `Please upload brand proof file for document ${fieldId}.`,
      };
    }

    if (!UUID_REGEX.test(fileId)) {
      return {
        isValid: false,
        error: `Please click the Submit button for brand proof document ${fieldId}.`,
      };
    }
  }

  return {
    isValid: true,
    error: null,
  };
};
