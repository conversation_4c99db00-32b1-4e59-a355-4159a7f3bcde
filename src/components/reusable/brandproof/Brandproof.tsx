'use client';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Button,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { Plus, Trash2 } from 'lucide-react';
import FileUpload from '@/components/reusable/file-upload/FileUpload';
import useBrandproof from './useBrandproof';
import { UseFormReturn } from 'react-hook-form';

interface BrandproofProps {
  form: UseFormReturn<any>;
}

const Brandproof: React.FC<BrandproofProps> = ({ form }) => {
  const { fileUploads, addMoreFiles, removeFileUpload, handleBlur, brandProofData } =
    useBrandproof(form);

  return (
    <div className="mx-auto w-full px-4 sm:max-w-4xl sm:px-0">
      <Form {...form}>
        <div className="-mx-3">
          {/* File Upload Sections with Sequential Brand Names */}
          <div className="-mx-5">
            {fileUploads.map((item, index) => {
              const sequentialNumber = index + 1;
              return (
                <div key={item.id} className="relative mb-4 p-4 sm:p-6">
                  {fileUploads.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2 h-7 w-7 p-0 sm:top-3 sm:right-10 sm:h-8 sm:w-8"
                      onClick={() => removeFileUpload(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}

                  {/* Individual Brand Name Field with Sequential Number */}
                  <FormField
                    control={form.control}
                    name={`brandProofName_${sequentialNumber}`}
                    render={({ field }) => (
                      <FormItem className="mb-4">
                        <FormLabel>
                          Brand Proof Name
                          <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={`Enter brand proof name`}
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                            onBlur={() => {
                              field.onBlur();
                              handleBlur();
                            }}
                            value={brandProofData[`brandProofName_${sequentialNumber}`] || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* File Upload with Sequential Number */}
                  <FileUpload
                    form={form}
                    component={{
                      key: `brandProofFileUpload_${sequentialNumber}`,
                      label: `Brand Proof Document`,
                      type: 'file',
                      input: true,
                      validate: { required: true },
                    }}
                  />
                </div>
              );
            })}
          </div>

          {/* Add More Files Button */}
          <div className="flex justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={addMoreFiles}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" /> Add brand proof
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default Brandproof;
