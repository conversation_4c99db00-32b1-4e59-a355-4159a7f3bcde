'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  PhoneInput,
} from '@kratex-tradetech/kratex-ui-library';
import { UseFormReturn } from 'react-hook-form';

type PhoneFieldProps = {
  form: UseFormReturn;
  name: string;
  isDisabled?: boolean;
};

const PhoneField = ({ form, name, isDisabled }: PhoneFieldProps) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>Phone Number</FormLabel>
          <FormControl>
            <PhoneInput
              value={field.value}
              onChange={field.onChange}
              defaultCountry="IN"
              disabled={isDisabled}
              className={isDisabled ? 'cursor-not-allowed' : ''}
              international
              countryCallingCodeEditable={false}
              placeholder="Enter phone number"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PhoneField;
