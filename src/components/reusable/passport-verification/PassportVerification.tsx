'use client';

import { useCustomDataStore } from '@/store/customData.store';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { useState } from 'react';

const PassportVerification = () => {
  const [passport, setPassport] = useState('');
  const { clearAllSandboxResponse, setModuleData } = useCustomDataStore();

  const handleBlur = () => {
    clearAllSandboxResponse();
    setModuleData({ passport });
  };

  return (
    <>
      <FormItem className="space-x-2">
        <FormLabel>
          Passport ID <span className="text-red-500">*</span>
        </FormLabel>
        <FormControl>
          <div className="relative">
            <Input
              value={passport}
              onChange={(e) => setPassport(e.target.value)}
              onBlur={handleBlur}
              placeholder="Enter your passport ID"
            />
          </div>
        </FormControl>
        <FormMessage />
      </FormItem>
    </>
  );
};

export default PassportVerification;
