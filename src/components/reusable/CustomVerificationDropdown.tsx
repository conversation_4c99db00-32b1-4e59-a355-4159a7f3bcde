'use client';

import { useCustomDataStore } from '@/store/customData.store';
import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import AadharVerification from './aadhaar-verification/AadharVerification';
import DrivingLicenseVerification from './driving-license-verification/DrivingLicenseVerification';
import PanVerification from './pan-verification/PanVerification';
import PassportVerification from './passport-verification/PassportVerification';

const CustomVerificationDropdown = ({ name, form }: { name: string; form: UseFormReturn }) => {
  const { clearAllSandboxResponse, setModuleData, clearAllModuleData } = useCustomDataStore();
  const dropDownValue = form.watch(name);

  // Clear all custom data when the dropdown value changes
  useEffect(() => {
    clearAllSandboxResponse();
    clearAllModuleData();
    setModuleData({ dropDownValue });
  }, [clearAllModuleData, clearAllSandboxResponse, dropDownValue, setModuleData]);

  switch (dropDownValue) {
    case 'aadhaar':
      return <AadharVerification />;
    case 'passport':
      return <PassportVerification />;
    case 'pan':
      return <PanVerification form={form} />;
    case 'drivingLicence':
      return <DrivingLicenseVerification />;
    default:
      return;
  }
};

export default CustomVerificationDropdown;
