import { usePostFileUpload } from '@/services/forms/file/usePostFile';
import { useFileIdsStore } from '@/store/fileIds.store';
import { toast } from 'sonner';

const useFileUpload = (doc_type: string) => {
  const { setFileId } = useFileIdsStore();
  const { mutate: uploadFile, isPending } = usePostFileUpload({
    onSuccess: ({ id }: { id: string }) => {
      toast.success(`File uploaded successfully!`);
      setFileId({ [doc_type]: id });
    },
    onError: () => toast.error('Failed to upload file!'),
  });

  const onSubmit = (file: File | null) => {
    if (!file) {
      toast.error('No file selected');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('doc_type', doc_type);

    uploadFile(formData);
  };

  return { onSubmit, isPending };
};

export default useFileUpload;
