'use client';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { useRouter } from 'next/navigation';

const CTAButton = () => {
  const router = useRouter();
  return (
    <Button
      onClick={() => {
        router.push('/login-signup');
      }}
      size="lg"
      className="px-8 py-3 text-lg"
    >
      Proceed to Login/Signup
    </Button>
  );
};

export default CTAButton;
