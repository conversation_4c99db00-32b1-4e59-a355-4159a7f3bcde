'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kratex-tradetech/kratex-ui-library';
import Image from 'next/image';

const AppInfoCard = () => {
  return (
    // Application Information Card with Image
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Powering Construction Trade & Supply Chain</CardTitle>
        <CardDescription>
          Simplify procurement, automate purchasing, and optimize supply chain operations—all in one
          intelligent platform built for the construction industry.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Application Image */}
        <div className="flex w-full justify-center">
          <div className="h-48 w-48 overflow-hidden rounded-lg">
            <Image
              src="/images/buldozer.png"
              alt="Application Preview"
              width={1920}
              height={500}
              className="h-full w-full object-cover"
            />
          </div>
        </div>
        <p className="text-muted-foreground text-sm">
          Kratex ERP makes construction procurement easy. Manage purchase orders and approvals
          faster. Work smoothly with suppliers to place and track orders. Generate accurate bills
          and stay compliant. Keep full control of your construction materials and inventory.
          Builders, contractors, and suppliers use Kratex to save time, spend less, and finish
          projects quicker—with no paperwork headaches.
        </p>
      </CardContent>
    </Card>
  );
};

export default AppInfoCard;
