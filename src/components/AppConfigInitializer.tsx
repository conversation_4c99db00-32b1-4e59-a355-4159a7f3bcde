'use client';
import useAppConfig from '@/hooks/useAppConfig';
import { useConfigStore } from '@/store/useAppConfig.store';
import { useEffect } from 'react';

interface AppConfigInitializerProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const AppConfigInitializer = ({ children }: AppConfigInitializerProps) => {
  const { isLoading, error, isError } = useAppConfig();
  const { isConfigLoaded, isConfigLoading } = useConfigStore();

  useEffect(() => {
    if (isError) {
      console.error('Failed to load app configuration:', error);
    }
  }, [isError, error]);

  // Show loading state while config is being loaded
  // TODO: maybe update this to a spinner or loader later
  if (isLoading || isConfigLoading || !isConfigLoaded) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center">
        Loading configuration...
      </div>
    );
  }

  // Show error state if config failed to load
  if (isError) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-lg font-semibold text-red-600">Configuration Error</h2>
          <p className="mb-4 text-gray-600">
            Failed to load application configuration. Please refresh the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AppConfigInitializer;
