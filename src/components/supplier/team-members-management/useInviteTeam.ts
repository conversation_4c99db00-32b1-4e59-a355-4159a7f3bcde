import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { useInviteTeam } from '@/services/auth/useInviteTeam';
import z from 'zod';
import { useSkip } from '@/services/forms/useSkip';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export const seatRoleOptions = ['CATALOG MANAGER', 'SALES REP', 'FINANCE', 'SUPPORT'] as const;

const FormSchema = z.object({
  teams: z
    .array(
      z.object({
        full_name: z.string().min(2, {
          message: 'Name must be at least 2 characters.',
        }),
        phone_number: z.string().min(8, 'Phone is required'),
        email: z.string().email('Invalid email'),
        role: z.enum(seatRoleOptions, {
          errorMap: () => ({ message: 'Please select a valid seat role' }),
        }),
      }),
    )
    .min(1, 'You have to invite at least one team member'),
});

type FormValues = z.infer<typeof FormSchema>;

export const useSupplierInviteTeam = () => {
  const router = useRouter();
  const { mutate: inviteSupplier } = useInviteTeam({
    onSuccess: () => {
      toast.success('Team invited successfully!');
      router.push('/supplier/finalize-onboarding');
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to invite team.');
    },
  });

  const { mutate: skip } = useSkip({
    onSuccess() {
      toast.info('Skipped Invited team. You can invite team members later from profile.');
      router.push('/supplier/finalize-onboarding');
    },
    onError() {
      toast.error('Skipping failed. Please try again');
    },
  });
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      teams: [
        {
          full_name: '',
          phone_number: '',
          email: '',
          role: 'CATALOG MANAGER',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'teams',
  });

  function onSubmit(data: FormValues) {
    inviteSupplier({ userTypeLabel: 'supplier', payload: data });
  }

  return {
    form,
    onSubmit,
    fields,
    append,
    remove,
    skip,
  };
};
