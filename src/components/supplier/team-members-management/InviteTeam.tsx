'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  PhoneInput,
  Progress,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';
import { Plus, TrashIcon, UserPlus } from 'lucide-react';
import {
  seatRoleOptions,
  useSupplierInviteTeam,
} from '@/components/supplier/team-members-management/useInviteTeam';

export function InviteTeam() {
  const { fields, append, remove, onSubmit, form, skip } = useSupplierInviteTeam();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex min-h-screen w-full flex-col items-center py-10"
      >
        {/* Progress Bar */}
        <div className="my-4 w-full max-w-4xl">
          <div className="flex items-center justify-between space-y-2">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-muted-foreground text-sm">{95}%</span>
          </div>
          <Progress value={95} className="w-full" />
        </div>

        <Card className="w-full max-w-4xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Invite Team Members
            </CardTitle>
            <CardDescription>
              Add team members to your supplier account. This step is optional and can be completed
              later.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {fields.map((field, index) => (
              <Card key={field.id} className="w-full">
                <CardHeader>
                  <CardTitle>Team Member {index + 1}</CardTitle>
                  <CardDescription>Fill in the details of your teammate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-x-4">
                    <FormField
                      control={form.control}
                      name={`teams.${index}.full_name`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="John Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`teams.${index}.phone_number`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <PhoneInput
                              value={field.value}
                              onChange={field.onChange}
                              defaultCountry="IN"
                              international
                              countryCallingCodeEditable={false}
                              placeholder="Enter phone number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex gap-x-4">
                    <FormField
                      control={form.control}
                      name={`teams.${index}.email`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`teams.${index}.role`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Seat Role</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a verified email to display" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {seatRoleOptions.map((option) => (
                                <SelectItem value={option} key={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:bg-red-600 hover:text-white"
                    disabled={fields.length === 1}
                  >
                    <TrashIcon /> Remove Member
                  </Button>
                </CardFooter>
              </Card>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={() =>
                append({
                  full_name: '',
                  phone_number: '',
                  email: '',
                  role: 'CATALOG MANAGER',
                })
              }
              className="my-5 w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Another Team Member
            </Button>
          </CardContent>
          <CardFooter className="flex flex-col gap-3 sm:flex-row">
            <Button
              onClick={() => skip({ state: 'mark_invite_member_skipped' })}
              type="button"
              variant="outline"
              className="flex-1"
            >
              Skip for Now
            </Button>
            <Button type="submit" className="flex-1">
              Send Invitations
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
