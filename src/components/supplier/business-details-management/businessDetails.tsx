'use client';

import { FormioFormRenderer, FormioSchema } from '@/utils/formio';
import { Button, Progress } from '@kratex-tradetech/kratex-ui-library';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import useBusinessDetails from './useBusinessDetails';
import { Loader, Loader2 } from 'lucide-react';

const SupplierBusinessDetailsForm = ({
  transformedSchema,
}: {
  transformedSchema: FormioSchema;
}) => {
  const { handleSubmit, isButtonDisabled, isFormLoading, isFormLoadError } = useBusinessDetails();

  const formioJson = transformedSchema as FormioSchema;

  const form = useDynamicZodForm({
    formioJson,
  });

  if (isFormLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="animate-spin" />
      </div>
    );
  }
  if (isFormLoadError) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p>Failed to load form, please try again later.</p>
      </div>
    );
  }

  return (
    <>
      <div className="my-4 w-full max-w-4xl">
        <div className="flex items-center justify-between space-y-2">
          <span className="text-sm font-medium">Setup Progress</span>
          <span className="text-muted-foreground text-sm">{60}%</span>
        </div>
        <Progress value={60} className="w-full" />
      </div>
      <FormioFormRenderer form={form} formioJson={formioJson} onSubmit={handleSubmit}>
        <Button disabled={isButtonDisabled} className="ml-auto block" type="submit">
          Proceed
        </Button>
      </FormioFormRenderer>
    </>
  );
};

const SupplierBusinessDetails = () => {
  const { transformedSchema } = useBusinessDetails();
  return transformedSchema ? (
    <SupplierBusinessDetailsForm transformedSchema={transformedSchema} />
  ) : (
    <div className="flex h-screen items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};

export default SupplierBusinessDetails;
