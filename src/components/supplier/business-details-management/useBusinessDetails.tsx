'use client';

import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { SubmitBusinessDetailsPayload } from '@/services/supplier/types';
import { useSubmitBusinessDetails } from '@/services/supplier/useSubmitBusinessDetails';
import { userStore } from '@/store/user.store';
import { FormioSchema } from '@/utils/formio';
import { injectPhoneNumberIntoSchema } from '@/utils/formio/formSchemaDataInjection';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import { toast } from 'sonner';

interface BusinessDetailsFormData {
  legal_business_name: string;
  registration_number: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  postcode: string;
  primary_contact_name: string;
  phone: string;
  support_email: string;
}

const useBusinessDetails = () => {
  const { supplierOnboardingData, userData, updateSupplierOnboardingData, updateUserData } =
    userStore();
  const router = useRouter();
  const {
    data: businessDetails,
    isError,
    isLoading,
  } = useGetFormDefinition('businessDetailsSupplierIndia');

  useEffect(() => {
    if (isError) {
      toast.error('Error loading the business details form, please try again.');
    }
  }, [isError, router]);

  const { mutate, isPending } = useSubmitBusinessDetails({
    onSuccess: (data) => {
      updateUserData({
        email: data.email,
        phone: data.phone,
        user_type: data.user_type,
      });
      updateSupplierOnboardingData({
        ...data,
        isEmailVerified: true,
      });
      toast.success('Business details submitted successfully!');
      router.push('/supplier/kyc');
    },
    onError: (error) =>
      toast.error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          'Failed to submit business details!',
      ),
  });

  const handleSubmit = (data: BusinessDetailsFormData) => {
    if (!supplierOnboardingData) {
      toast.error('Failed to submit business details! Please fill all the required data.');
      return;
    }
    const payload: SubmitBusinessDetailsPayload = {
      email: data.support_email, // user email
      name: data.primary_contact_name, // user name
      country_of_origin: supplierOnboardingData.jurisdiction.countryOfOrigin.iso2, // confirmed with backend for iso2
      organisation_type: supplierOnboardingData.orgType.map((item) => item.id),
      countries_to_serve: supplierOnboardingData.jurisdiction.countriesToServe.map(
        (item) => item.id,
      ),
      supplier_details: data,
    };
    mutate(payload);
  };

  const transformedSchema = useMemo(() => {
    return businessDetails?.schema && userData?.phone
      ? injectPhoneNumberIntoSchema(businessDetails.schema as FormioSchema, userData?.phone)
      : undefined;
  }, [businessDetails?.schema, userData?.phone]);

  return {
    handleSubmit,
    transformedSchema,
    isButtonDisabled: isPending,
    isFormLoading: isLoading,
    isFormLoadError: isError,
  };
};

export default useBusinessDetails;
