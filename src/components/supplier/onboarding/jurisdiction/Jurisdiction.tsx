'use client';

import MultiSelectDropdown from '@/components/common/MultiSelectDropdown/MultiSelectDropdown';
import {
  <PERSON>ge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CountryDropdown,
  Label,
  Progress,
} from '@kratex-tradetech/kratex-ui-library';
import { ChevronRight, Globe, MapPin } from 'lucide-react';
import { useJurisdiction } from './useJurisdiction';

const Jurisdiction = () => {
  const {
    countryServeOptions,
    selectedCountryServeOptions,
    countryOfOrigin,
    countriesToServe,
    handleMultiSelectCountryToServe,
    handleCountryOfOriginSelect,
    loadingCountries,
    validateForm,
    onJurisdictionSubmit,
    progress,
  } = useJurisdiction();

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-lg space-y-6">
        {/* Header */}
        <div className="space-y-2 text-center">
          <div className="mb-4 flex items-center justify-center">
            <div className="rounded-full p-3">
              <Globe className="h-8 w-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Jurisdiction Information</h1>
          <p className="text-muted-foreground">
            Please select your country of origin and the countries you serve
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-muted-foreground text-sm">{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        {/* Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Details
            </CardTitle>
            <CardDescription>
              This information helps us provide region-specific services and compliance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Country of Origin */}
            <div className="space-y-2">
              <Label>Country of Origin</Label>
              <CountryDropdown
                defaultValue={countryOfOrigin?.alpha3 || countryOfOrigin?.iso3}
                onChange={handleCountryOfOriginSelect}
                placeholder="Select a country"
                disabled={loadingCountries}
              />
            </div>

            {/* Countries to Serve */}
            <div className="space-y-2">
              <Label htmlFor="countries-serve">Countries You Serve</Label>
              <MultiSelectDropdown
                placeholder="Select countries you serve"
                options={countryServeOptions}
                selectedValues={selectedCountryServeOptions}
                onSelectionChange={handleMultiSelectCountryToServe}
                id="countries-serve-multiselect"
                isDataLoading={loadingCountries}
              />
            </div>

            {/* Selected Values Summary */}
            {(countryOfOrigin || countriesToServe.length > 0) && (
              <div className="rounded-lg border p-4">
                <h3 className="mb-3 flex items-center gap-2 text-sm font-semibold">
                  <MapPin className="h-4 w-4" />
                  Selected Jurisdiction
                </h3>
                <div className="space-y-3">
                  {countryOfOrigin && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-sm">Country of Origin:</span>
                      <Badge variant="secondary">
                        <span className="mr-1">{countryOfOrigin.emoji}</span>
                        {countryOfOrigin.name}
                      </Badge>
                    </div>
                  )}
                  {countriesToServe.length > 0 && (
                    <div className="space-y-2">
                      <span className="text-muted-foreground text-sm">Countries You Serve:</span>
                      <div className="flex flex-wrap gap-1">
                        {countriesToServe.map((country) => (
                          <Badge key={country.id} variant="outline">
                            <span className="mr-1">{country.emoji}</span>
                            {country.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end">
          <Button
            onClick={onJurisdictionSubmit}
            disabled={!validateForm()}
            className="flex items-center space-x-2 rounded-lg px-8 py-3 font-medium transition-all"
          >
            <span>Proceed</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Jurisdiction;
