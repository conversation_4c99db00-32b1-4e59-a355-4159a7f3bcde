'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Progress,
} from '@kratex-tradetech/kratex-ui-library';

import { CheckCircle, CircleAlert } from 'lucide-react';
import { useFinalizeOnboarding } from './useFinalizeOnboarding';

export default function OnboardingCompletion() {
  const { progress, handleConfirmFinish, isSubmitting, onboardingState } = useFinalizeOnboarding();

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Onboarding Progress</span>
              <span className="font-semibold">100%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
          <div className="mx-auto my-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-green-600">Congratulations!</CardTitle>
          <CardDescription className="text-base">
            Your supplier account is now fully set up and ready to go
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-muted-foreground space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Jurisdiction completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Organization Type Selected</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Buisness Details completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>KYC completed</span>
            </div>
            {onboardingState === 'mark_member_invited' ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Team Invite Completed</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CircleAlert className="h-4 w-4 text-yellow-500" />
                <span>Team Invite Skipped</span>
              </div>
            )}
          </div>

          <Button
            disabled={isSubmitting}
            onClick={handleConfirmFinish}
            className="w-full"
            size="lg"
          >
            Go to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
