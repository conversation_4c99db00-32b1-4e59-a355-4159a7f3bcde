'use client';

import { useRouter } from 'next/navigation';
import { useFinalizeOnboarding as useFinalizeOnboardingSupplier } from '@/services/auth/useFinalizeOnboarding';
import { toast } from 'sonner';
import { userStore } from '@/store/user.store';

export const useFinalizeOnboarding = () => {
  const router = useRouter();
  const { userData } = userStore();

  const onboardingState = userData?.onboarding_state;

  const progress = 100;

  const { mutate: finaliseSubmit, isPending } = useFinalizeOnboardingSupplier({
    onSuccess(data) {
      toast.success(data.message);
      router.push('/supplier/dashboard');
    },
    onError: () => {
      toast.error('Failed to finalise onboarding, Please try again!');
    },
  });

  const handleConfirmFinish = () => finaliseSubmit('supplier');

  return {
    progress,
    isSubmitting: isPending,
    handleConfirmFinish,
    onboardingState,
  };
};
