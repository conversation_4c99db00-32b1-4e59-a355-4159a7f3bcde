'use client';

import useBuyingAgentIndia from './useSupplierUnifiedKycIndia';
import { Loader } from 'lucide-react';
import { Button, Progress } from '@kratex-tradetech/kratex-ui-library';
import { FormioFormResponse, FormioSchema } from '@/utils/formio/types';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { FormioFormRenderer } from '@/utils/formio';

const SupplierIndiaComponent = ({ formSchema }: { formSchema: FormioFormResponse }) => {
  const { isLoading, isSuccess, handleSubmit, isPostKycPending } = useBuyingAgentIndia();

  const form = useDynamicZodForm({
    formioJson: formSchema?.schema as FormioSchema,
  });

  if (isLoading) return <Loader className="animate-spin" />;
  return (
    isSuccess &&
    formSchema && (
      <div className="flex min-h-screen w-full flex-col items-center justify-center p-8">
        {/* Progress Bar */}
        <div className="my-4 w-full max-w-4xl">
          <div className="flex items-center justify-between space-y-2">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-muted-foreground text-sm">{80}%</span>
          </div>
          <Progress value={80} className="w-full" />
        </div>
        <FormioFormRenderer
          form={form}
          onSubmit={handleSubmit}
          formioJson={formSchema.schema as FormioSchema}
        >
          <div className="flex w-full flex-row items-center justify-between gap-x-4">
            <Button className="flex w-full" type="submit" disabled={isPostKycPending}>
              Proceed
            </Button>
          </div>
        </FormioFormRenderer>
      </div>
    )
  );
};

const SupplierIndia = () => {
  const { formSchema } = useBuyingAgentIndia();

  return formSchema ? (
    <SupplierIndiaComponent formSchema={formSchema} />
  ) : (
    <div className="flex h-screen w-full items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};

export default SupplierIndia;
