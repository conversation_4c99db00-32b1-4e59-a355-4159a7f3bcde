import { useRouter } from 'next/navigation';
import { useSubmitKYCForm } from '@/services/kyc/useSubmitKYCForm';
import { useCustomDataStore } from '@/store/customData.store';
import { useFileIdsStore } from '@/store/fileIds.store';
import { useEffect } from 'react';
import { toast } from 'sonner';
import { kycPayloadGenerator } from '@/utils/formio/kycPayloadGenerator';
import { KYCStatusData } from '@/constants/kyc-status';
import { checkPayloadForBrandProof } from '@/components/reusable/brandproof/checkBrandProofPayload';
import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';

const useBuyingAgentIndia = () => {
  const router = useRouter();
  const {
    data: formSchema,
    isSuccess,
    isLoading,
    isError,
  } = useGetFormDefinition('supplierUnifiedKycIndia');

  const { mutate: postsupplierIndiaKyc, isPending: isPostKycPending } = useSubmitKYCForm({
    onSuccess: handleSubmitSuccess,
    onError: handleSubmitError,
  });

  const { moduleData } = useCustomDataStore();

  const { fileIds, clearFileIds } = useFileIdsStore();

  useEffect(() => {
    if (isError) {
      toast.error('Error loading the KYC form, please try again.');
      router.push('/supplier/invite-team');
    }
  }, [isError, router]);

  const handleSubmit = (data: Record<string, string>) => {
    if (!moduleData || !formSchema) return;

    const { brandProofData, ...restModuleData } = moduleData as Record<string, unknown>;
    const brandProofObj = brandProofData as Record<string, string>;

    const updatedData = {
      ...data,
      ...restModuleData,
      ...brandProofObj,
    };

    const stringifiedData: Record<string, string> = Object.fromEntries(
      Object.entries(updatedData).map(([key, value]) => [
        key,
        value !== undefined && value !== null ? String(value) : '',
      ]),
    );

    const payload = kycPayloadGenerator(
      fileIds,
      formSchema.keyword,
      stringifiedData,
      KYCStatusData.UNDER_REVIEW.value,
    );
    const validation = checkPayloadForBrandProof(payload);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }
    postsupplierIndiaKyc(payload);
  };

  function handleSubmitSuccess() {
    toast.success('Kyc completed successfully!');
    clearFileIds();
    router.push('/supplier/invite-team');
  }
  function handleSubmitError() {
    toast.error('Kyc failed!');
  }

  return {
    isError,
    formSchema,
    isLoading,
    isSuccess,
    isPostKycPending,
    handleSubmit,
  };
};

export default useBuyingAgentIndia;
