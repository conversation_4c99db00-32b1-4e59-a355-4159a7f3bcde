'use client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Button,
  Progress,
  Checkbox,
} from '@kratex-tradetech/kratex-ui-library';
import { ChevronRight, Loader2 } from 'lucide-react';
import { useOrganisationType } from './useOrganisationType';

const SpecifyOrganisationType = () => {
  const { selectedTypes, organisationTypes, isLoading, handleTypeToggle, proceed, progress } =
    useOrganisationType();

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-8">
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Specify Organisation Type</h1>
          <p className="text-muted-foreground text-lg">
            Please select the type of organisations that represent your business. This helps us
            tailor the experience to your needs.
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-muted-foreground text-sm">{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        {/* Options Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">
              Select one or more organisation types that best describe your business
            </CardTitle>
            <CardDescription>Organisation Details</CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                <p>Loading organisation types...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {organisationTypes.map((type) => (
                  <div
                    key={type.name}
                    className={`flex cursor-pointer items-start space-x-3 rounded-lg border-2 p-3 transition-all ${
                      selectedTypes.includes(type.name)
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={(e) => {
                      if (e.target instanceof HTMLInputElement) return;
                      handleTypeToggle(type.name);
                    }}
                  >
                    <Checkbox
                      id={type.name}
                      checked={selectedTypes.includes(type.name)}
                      className="mt-1 border border-gray-400 data-[state=checked]:bg-black data-[state=checked]:text-white"
                    />
                    <Label htmlFor={type.name} className="cursor-pointer text-base font-medium">
                      {type.name}
                    </Label>
                  </div>
                ))}
              </div>
            )}
            {/* Show selected types as badges */}
            {selectedTypes.length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedTypes.map((typeName) => (
                  <span key={typeName} className="bg-muted inline-block rounded px-2 py-1 text-xs">
                    {typeName}
                  </span>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Proceed Button */}
        <div className="flex justify-end">
          <Button
            onClick={proceed}
            disabled={selectedTypes.length === 0}
            className="flex items-center space-x-2 px-8 py-3"
          >
            <span>Proceed</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SpecifyOrganisationType;
