'use client';

import { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useGetOrganisationTypes } from '@/services/auth/useGetOrganisationType';
import { OrganisationTypeOption } from './types';
import { userStore } from '@/store/user.store';

export const useOrganisationType = () => {
  const router = useRouter();
  const { updateSupplierOnboardingData, supplierOnboardingData, userData } = userStore();

  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

  const {
    data: apiOrganisationTypes = [],
    isLoading,
    error,
  } = useGetOrganisationTypes(
    supplierOnboardingData?.jurisdiction?.countryOfOrigin?.iso2 || '',
    String(userData?.user_type), // role (number not the label)
  );

  const staticOrganisationTypes: OrganisationTypeOption[] = [
    { id: 1, name: 'Manufacturer' },
    { id: 2, name: 'Brand owner' },
    { id: 3, name: 'Authorised Distributor/Dealer' },
    { id: 4, name: 'Trader/Wholesaler' },
    { id: 5, name: 'Service provider' },
  ];

  const organisationTypes: OrganisationTypeOption[] = useMemo(() => {
    return apiOrganisationTypes.length > 0 ? apiOrganisationTypes : staticOrganisationTypes;
  }, [apiOrganisationTypes]);

  const progress = 40;

  const proceed = () => {
    if (selectedTypes.length === 0 || isLoading || error) return;

    if (selectedTypes.length > 0) {
      const selectedOrgs = organisationTypes.filter((type: OrganisationTypeOption) =>
        selectedTypes.includes(type.name),
      );
      if (selectedOrgs.length > 0) {
        updateSupplierOnboardingData({
          orgType: selectedOrgs.map((org) => ({ id: org.id, name: org.name })),
        });
      }
      router.push('/supplier/business-details');
    }
  };

  // Multi-select handler
  const handleTypeToggle = (typeName: string) => {
    setSelectedTypes((prev) =>
      prev.includes(typeName) ? prev.filter((name) => name !== typeName) : [...prev, typeName],
    );
  };

  return {
    isLoading,
    selectedTypes,
    setSelectedTypes,
    organisationTypes,
    proceed,
    error,
    progress,
    handleTypeToggle,
  };
};
