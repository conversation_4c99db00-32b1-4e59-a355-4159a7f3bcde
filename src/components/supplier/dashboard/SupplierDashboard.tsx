'use client';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Input,
  Label,
  Separator,
} from '@kratex-tradetech/kratex-ui-library';
import {
  Badge,
  BarChart3,
  Bell,
  Package,
  Plus,
  Search,
  Settings,
  Sparkles,
  Users,
} from 'lucide-react';
import { useState } from 'react';

// Tour Hotspot component
function TourHotspot({
  children,
  title,
  description,
  position = 'bottom',
  isActive,
  onNext,
  onSkip,
}: {
  children: React.ReactNode;
  title: string;
  description: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  isActive: boolean;
  onNext: () => void;
  onSkip: () => void;
}) {
  if (!isActive) return <>{children}</>;

  return (
    <div className="relative">
      <div className="relative z-10 rounded-lg ring-2 ring-blue-500 ring-offset-2">{children}</div>
      <div
        className={`absolute z-20 w-64 rounded-lg border bg-white p-4 shadow-lg ${
          position === 'bottom'
            ? 'top-full mt-2'
            : position === 'top'
              ? 'bottom-full mb-2'
              : position === 'right'
                ? 'left-full ml-2'
                : 'right-full mr-2'
        }`}
      >
        <h3 className="mb-2 text-sm font-semibold">{title}</h3>
        <p className="text-muted-foreground mb-3 text-xs">{description}</p>
        <div className="flex gap-2">
          <Button size="sm" onClick={onNext}>
            Next
          </Button>
          <Button size="sm" variant="outline" onClick={onSkip}>
            Skip Tour
          </Button>
        </div>
      </div>
    </div>
  );
}

const SupplierDashboard = () => {
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [tourStep, setTourStep] = useState(0);

  const handleNextTour = () => {
    if (tourStep < 1) {
      setTourStep(tourStep + 1);
    } else {
      setTourStep(-1); // End tour
    }
  };

  const handleSkipTour = () => {
    setTourStep(-1);
  };

  const handleAddMoreBrands = () => {
    setShowBrandModal(true);
    setTourStep(-1);
  };

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 border-b px-8 backdrop-blur">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <span className="font-bold">Kratex Trading</span>
          </div>

          <div className="flex max-w-sm flex-1 items-center space-x-2">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  className="pl-8 md:w-[300px] lg:w-[400px]"
                />
              </div>
            </div>
          </div>

          <div className="ml-auto flex items-center space-x-2">
            <Button variant="ghost" size="icon">
              <Bell className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm leading-none font-medium">John Doe</p>
                    <p className="text-muted-foreground text-xs leading-none"><EMAIL></p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="container mx-auto p-6">
        {/* Welcome Banner */}
        <div className="mb-6 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <h1 className="mb-2 text-2xl font-bold">Welcome to your dashboard, John! 🎉</h1>
          <p className="opacity-90">
            Your Siemens catalogue is now live and ready for buyers to discover.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">312</div>
              <p className="text-muted-foreground text-xs">+12 from last month</p>
            </CardContent>
          </Card>

          <TourHotspot
            title="Live Products Badge"
            description="This shows how many of your products are currently published and visible to buyers. Great job getting 312 items live!"
            isActive={tourStep === 0}
            onNext={handleNextTour}
            onSkip={handleSkipTour}
          >
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Live Products</CardTitle>
                <Badge className="bg-green-100 text-green-800">
                  <Sparkles className="mr-1 h-3 w-3" />
                  Live
                </Badge>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">312</div>
                <p className="text-xs text-green-600">items published</p>
              </CardContent>
            </Card>
          </TourHotspot>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Buyers</CardTitle>
              <Users className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-muted-foreground text-xs">+180 this week</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <BarChart3 className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">€45,231</div>
              <p className="text-muted-foreground text-xs">+20.1% from last month</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your catalogue and expand your reach</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start bg-transparent" variant="outline">
                <Package className="mr-2 h-4 w-4" />
                Upload New Products
              </Button>

              <TourHotspot
                title="Add More Brands"
                description="Expand your catalogue by adding products from additional brands. This will help you reach more buyers and increase your sales potential."
                position="right"
                isActive={tourStep === 1}
                onNext={handleNextTour}
                onSkip={handleSkipTour}
              >
                <Button
                  className="w-full justify-start bg-transparent"
                  variant="outline"
                  onClick={handleAddMoreBrands}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add More Brands
                </Button>
              </TourHotspot>

              <Button className="w-full justify-start bg-transparent" variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Analytics
              </Button>

              <Button className="w-full justify-start bg-transparent" variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Account Settings
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates on your products and orders</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Siemens catalogue published</p>
                    <p className="text-muted-foreground text-xs">312 products are now live</p>
                  </div>
                  <span className="text-muted-foreground text-xs">Just now</span>
                </div>

                <Separator />

                <div className="flex items-center space-x-4">
                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New buyer inquiry</p>
                    <p className="text-muted-foreground text-xs">
                      TechCorp requested quote for industrial sensors
                    </p>
                  </div>
                  <span className="text-muted-foreground text-xs">2 hours ago</span>
                </div>

                <Separator />

                <div className="flex items-center space-x-4">
                  <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Product update required</p>
                    <p className="text-muted-foreground text-xs">5 products need price updates</p>
                  </div>
                  <span className="text-muted-foreground text-xs">1 day ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Brand Modal */}
      <Dialog open={showBrandModal} onOpenChange={setShowBrandModal}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Add More Brands</DialogTitle>
            <DialogDescription>
              Expand your catalogue by adding products from additional brands to reach more buyers.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="brand" className="text-right">
                Brand Name
              </Label>
              <Input id="brand" placeholder="e.g., Schneider Electric" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="category" className="text-right">
                Category
              </Label>
              <Input
                id="category"
                placeholder="e.g., Industrial Automation"
                className="col-span-3"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowBrandModal(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowBrandModal(false)}>Add Brand</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SupplierDashboard;
