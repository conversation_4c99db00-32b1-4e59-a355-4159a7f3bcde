'use client';

import { ChevronDown, X, Check, Search } from 'lucide-react';
import { useMultiSelectDropdown } from './useMultiSelectDropdown';
import { DropdownOption } from './types';

interface MultiSelectDropdownProps {
  placeholder: string;
  options: DropdownOption[];
  selectedValues: DropdownOption[];
  onSelectionChange: (value: DropdownOption) => void;
  id?: string;
  isDataLoading?: boolean;
}

const MultiSelectDropdown = ({
  placeholder,
  options,
  selectedValues,
  onSelectionChange,
  id,
  isDataLoading = false,
}: MultiSelectDropdownProps) => {
  const {
    isOpen,
    searchTerm,
    filteredOptions,
    showSearch,
    dropdownRef,
    searchInputRef,
    handleOptionClick,
    handleRemoveTag,
    handleSearchChange,
    handleDropdownToggle,
  } = useMultiSelectDropdown(options, selectedValues, onSelectionChange);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger */}
      <div
        id={id}
        className="border-input bg-background ring-offset-background focus:ring-ring flex min-h-10 w-full cursor-pointer items-center justify-between rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none"
        onClick={handleDropdownToggle}
      >
        <div className="flex-1">
          {isDataLoading ? (
            <span className="text-muted-foreground">...data loading</span>
          ) : selectedValues.length === 0 ? (
            <span className="text-muted-foreground">{placeholder}</span>
          ) : (
            <div className="flex flex-wrap gap-1">
              {selectedValues.map((value) => (
                <span
                  key={value.id}
                  className="bg-secondary inline-flex items-center gap-1 rounded px-2 py-1 text-xs"
                >
                  {value.name}
                  <button
                    type="button"
                    onClick={(e) => handleRemoveTag(value, e)}
                    className="hover:bg-secondary-foreground/20 rounded-full p-0.5"
                  >
                    <X size={12} />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown */}
      {isOpen && !isDataLoading && (
        <div className="bg-popover border-border absolute z-50 mt-1 max-h-60 w-full overflow-hidden rounded-md border shadow-md">
          {/* Search Input */}
          {showSearch && (
            <div className="border-border border-b p-2">
              <div className="relative">
                <Search
                  size={16}
                  className="text-muted-foreground absolute top-1/2 left-2 -translate-y-1/2 transform"
                />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search options..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="bg-background border-input focus:ring-ring w-full rounded-md border py-2 pr-3 pl-8 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="max-h-48 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className="text-muted-foreground px-3 py-2 text-sm">No options found</div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = selectedValues.some((selected) => selected.id === option.id);
                return (
                  <div
                    key={option.id}
                    className="hover:bg-accent hover:text-accent-foreground flex cursor-pointer items-center justify-between px-3 py-2 text-sm"
                    onClick={() => handleOptionClick(option)}
                  >
                    <span>{option.name}</span>
                    {isSelected && <Check size={16} className="text-primary" />}
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
