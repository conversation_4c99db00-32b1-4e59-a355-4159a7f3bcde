'use client';

import { Formio<PERSON><PERSON><PERSON><PERSON><PERSON>, FormioSchema } from '@/utils/formio';
import useBuyingAgentIndia from './useBuyingAgentIndia';
import { Loader } from 'lucide-react';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { FormioFormResponse } from '@/utils/formio/types';
import useKycSkip from '../useKycSkip';

const BuyingAgentIndiaComponent = ({ formSchema }: { formSchema: FormioFormResponse }) => {
  const { isLoading, isSuccess, handleSubmit, isPostKycPending } = useBuyingAgentIndia();
  const { skipKyc, isSkipPending } = useKycSkip();

  const form = useDynamicZodForm({
    formioJson: formSchema?.schema as FormioSchema,
  });

  // TODO: make a reusable loader spinner, this is to be replaced
  if (isLoading) return <Loader className="animate-spin" />;
  return (
    isSuccess &&
    formSchema && (
      <div className="flex min-h-screen flex-col items-center justify-center p-8">
        <div className="mb-6 text-center">
          <h2 className="mb-2 text-2xl font-semibold">KYC Form - Buying Agent (India)</h2>
          <p className="text-gray-600">
            Please fill out the following details to complete your Know Your Customer (KYC) process
            as a Buying Agent in India.
          </p>
        </div>
        <FormioFormRenderer
          form={form}
          onSubmit={handleSubmit}
          formioJson={formSchema.schema as FormioSchema}
        >
          <div className="flex w-full flex-row items-center justify-between gap-x-4">
            <Button
              className="flex w-[calc(50%-8px)]"
              type="button"
              variant="outline"
              disabled={isPostKycPending || isSkipPending}
              onClick={skipKyc}
            >
              Skip
            </Button>
            <Button
              className="flex w-[calc(50%-8px)]"
              type="submit"
              disabled={isPostKycPending || isSkipPending}
            >
              Proceed
            </Button>
          </div>
        </FormioFormRenderer>
      </div>
    )
  );
};

const BuyingAgentIndia = () => {
  const { formSchema } = useBuyingAgentIndia();

  return formSchema ? (
    <BuyingAgentIndiaComponent formSchema={formSchema} />
  ) : (
    <div className="flex h-screen items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};

export default BuyingAgentIndia;
