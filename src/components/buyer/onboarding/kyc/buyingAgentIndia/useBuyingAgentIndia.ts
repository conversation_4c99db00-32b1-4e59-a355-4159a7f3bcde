import { useSubmitKYCForm } from '@/services/kyc/useSubmitKYCForm';
import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { useEffect } from 'react';
import { toast } from 'sonner';
import { KYCStatusData } from '@/constants/kyc-status';
import { useFileIdsStore } from '@/store/fileIds.store';
import { useRouter } from 'next/navigation';
import { kycPayloadGenerator } from '@/utils/formio/kycPayloadGenerator';

const useBuyingAgentIndia = () => {
  const router = useRouter();
  const {
    data: formSchema,
    isSuccess,
    isLoading,
    isError,
  } = useGetFormDefinition('kycBuyerBuyingAgentIndia');

  const { mutate: postKYCBuyingAgent, isPending: isPostKycPending } = useSubmitKYCForm({
    onSuccess: handleSubmitSuccess,
    onError: handleSubmitError,
  });

  const { fileIds, clearFileIds } = useFileIdsStore();

  useEffect(() => {
    if (isError) {
      toast.error('Error loading the KYC form, please try again.');
      router.push('/buyer/sourcing-intent');
    }
  }, [isError, router]);

  const handleSubmit = (data: Record<string, string>) => {
    if (formSchema) {
      const payload = kycPayloadGenerator(
        fileIds,
        formSchema.keyword,
        data,
        KYCStatusData.UNDER_REVIEW.value,
      );

      postKYCBuyingAgent(payload);
    }
  };

  const skipSubmit = () => router.push('/buyer/sourcing-intent');

  function handleSubmitSuccess() {
    toast.success('Kyc completed successfully!');
    clearFileIds();
    router.push('/buyer/sourcing-intent');
  }
  function handleSubmitError() {
    toast.error('Kyc failed!');
  }

  return {
    isError,
    formSchema,
    isLoading,
    isSuccess,
    handleSubmit,
    skipSubmit,
    isPostKycPending,
  };
};

export default useBuyingAgentIndia;
