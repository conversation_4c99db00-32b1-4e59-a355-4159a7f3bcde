'use client';

import FileUpload from '@/components/reusable/file-upload/FileUpload';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { useSubmitKYCForm } from '@/services/kyc/useSubmitKYCForm';
import { useCustomDataStore } from '@/store/customData.store';
import { useFileIdsStore } from '@/store/fileIds.store';
import { GstinLookupResponse } from '@/types/sandboxVerification.types';
import { FormioFormRenderer, FormioSchema } from '@/utils/formio';
import { kycPayloadGenerator } from '@/utils/formio/kycPayloadGenerator';
import { FormioFormResponse } from '@/utils/formio/types';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { ArrowRight, Loader } from 'lucide-react';
import { toast } from 'sonner';
import useKycSkip from '../useKycSkip';

const RegisteredCompanyIndiaForm = ({ formioForm }: { formioForm: FormioFormResponse }) => {
  const { mutate, isPending: isPostKycPending } = useSubmitKYCForm({
    onSuccess() {
      toast.success('KYC submitted successfully!');
    },
    onError() {
      toast.error('Something went wrong');
    },
  });

  const { skipKyc, isSkipPending } = useKycSkip();

  const { sandboxResponse } = useCustomDataStore();
  const status = (sandboxResponse?.gstResponse as GstinLookupResponse)?.status;

  const form = useDynamicZodForm({
    formioJson: formioForm?.schema as FormioSchema,
  });

  const { fileIds } = useFileIdsStore();

  const constitution = (sandboxResponse?.gstResponse as GstinLookupResponse)?.constitution;
  const isPrivatePublic =
    constitution?.toLowerCase().includes('private') ||
    constitution?.toLowerCase().includes('public');

  const handleSubmit = (data: Record<string, string>) => {
    // if status is 0, means GSTN verified
    if (formioForm && status === 0) {
      const payload = kycPayloadGenerator(
        fileIds,
        formioForm.keyword,
        data,
        status, // status based on the gstin verification
      );
      mutate(payload);
    }
  };

  const component = {
    key: 'boardAuthorizationFileUpload',
    label: 'Board Authorization',
    type: 'file',
    input: true,
    validate: {
      required: true,
    },
  };

  // if (isLoading) return <LoadingState />;

  return (
    <section className="m-10 mx-auto max-w-4xl">
      {formioForm && (
        <div className="flex min-h-screen flex-col items-center justify-center p-8">
          <div className="mb-6 text-center">
            <h2 className="mb-2 text-2xl font-semibold">
              KYC Verification - Registered Company (India)
            </h2>
            <p className="text-gray-600">
              Please provide the required details below to complete the KYC verification process as
              a registered company in India.
            </p>
          </div>
          <FormioFormRenderer
            form={form}
            onSubmit={handleSubmit}
            formioJson={formioForm.schema as FormioSchema}
            showCard={true}
          >
            {isPrivatePublic && <FileUpload component={component} form={form} />}
            <div className="flex flex-row items-center gap-x-4 justify-self-end">
              <Button
                className="flex w-fit px-6"
                type="button"
                variant="outline"
                disabled={isPostKycPending || isSkipPending}
                onClick={skipKyc}
              >
                Skip
              </Button>
              <Button
                className="flex w-fit px-5"
                type="submit"
                disabled={status !== 0 || isPostKycPending || isSkipPending}
              >
                Proceed <ArrowRight />
              </Button>
            </div>
          </FormioFormRenderer>
        </div>
      )}
    </section>
  );
};

//TODO: refactor this component into a reusable component
const RegisteredCompanyIndia = () => {
  const { data: formioForm } = useGetFormDefinition('kycBuyerRegisteredCompanyIndia');

  return formioForm ? (
    <RegisteredCompanyIndiaForm formioForm={formioForm as FormioFormResponse} />
  ) : (
    <div className="flex h-screen items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};

export default RegisteredCompanyIndia;
