'use client';
import { useSkip } from '@/services/forms/useSkip';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const useKycSkip = () => {
  const router = useRouter();
  const { mutate, isPending: isSkipPending } = useSkip({
    onSuccess() {
      toast.info('KYC skipped. You can complete it later from your profile.');
      router.push('/buyer/sourcing-intent');
    },
    onError() {
      toast.error('Error skipping the KYC. Please try again.');
    },
  });
  return { skipKyc: () => mutate({ state: 'mark_user_kyc_skipped' }), isSkipPending };
};

export default useKycSkip;
