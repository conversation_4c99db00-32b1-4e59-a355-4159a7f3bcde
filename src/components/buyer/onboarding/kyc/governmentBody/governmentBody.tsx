'use client';

import { Formio<PERSON>orm<PERSON><PERSON><PERSON>, FormioSchema } from '@/utils/formio';
import useNonProfitIndia from './useGovernmentBodyIndia';
import { Loader } from 'lucide-react';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { FormioFormResponse } from '@/utils/formio/types';
import useKycSkip from '../useKycSkip';

const GovernmentBodyIndiaComponent = ({ formSchema }: { formSchema: FormioFormResponse }) => {
  const { isLoading, isSuccess, handleSubmit, isPostKycPending } = useNonProfitIndia();
  const { skipKyc, isSkipPending } = useKycSkip();

  const form = useDynamicZodForm({
    formioJson: formSchema?.schema as FormioSchema,
  });

  // TODO: make a reusable loader spinner, this is to be replaced
  if (isLoading) return <Loader className="animate-spin" />;
  return (
    isSuccess &&
    formSchema && (
      <div className="flex min-h-screen flex-col items-center justify-center p-8">
        <div className="mb-6 text-center">
          <h2 className="mb-2 text-2xl font-semibold">
            KYC Verification - Government Body (India)
          </h2>
          <p className="text-gray-600">
            Please provide the required details below to complete the KYC verification process as a
            government body in India.
          </p>
        </div>
        <FormioFormRenderer
          form={form}
          onSubmit={handleSubmit}
          formioJson={formSchema.schema as FormioSchema}
        >
          <div className="flex w-full flex-row items-center justify-between gap-x-4">
            <Button
              className="flex w-[calc(50%-8px)]"
              type="button"
              variant="outline"
              disabled={isPostKycPending || isSkipPending}
              onClick={skipKyc}
            >
              Skip
            </Button>
            <Button
              className="flex w-[calc(50%-8px)]"
              type="submit"
              disabled={isPostKycPending || isSkipPending}
            >
              Proceed
            </Button>
          </div>
        </FormioFormRenderer>
      </div>
    )
  );
};

//TODO: refactor this component into a reusable component
const GovernmentBodyIndia = () => {
  const { formSchema } = useNonProfitIndia();

  return formSchema ? (
    <GovernmentBodyIndiaComponent formSchema={formSchema as FormioFormResponse} />
  ) : (
    <div className="flex h-screen items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};

export default GovernmentBodyIndia;
