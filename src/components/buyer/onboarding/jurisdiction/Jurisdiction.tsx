'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CountryDropdown,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';
import { ChevronRight, Globe, MapPin, Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useJurisdiction } from './useJurisdiction';

const Jurisdiction = () => {
  const {
    states,
    selectedCountry,
    selectedState,
    loadingCountries,
    loadingStates,
    handleUICountrySelect,
    setSelectedState,
    getStateLabel,
    validateForm,
    onJurisdictionSubmit,
  } = useJurisdiction();

  const [stateSearch, setStateSearch] = useState('');
  const [isStateSelectOpen, setIsStateSelectOpen] = useState(false);

  const stateLabel = useMemo(() => {
    return selectedCountry ? getStateLabel(selectedCountry.iso3) : 'State/Province';
  }, [selectedCountry, getStateLabel]);

  const filteredStates = useMemo(() => {
    const term = stateSearch.toLowerCase().trim();
    return term ? states.filter((s) => s.name.toLowerCase().includes(term)) : states;
  }, [states, stateSearch]);

  const handleStateSelect = (stateId: string) => {
    const state = states.find((s) => s.id.toString() === stateId) || null;
    setSelectedState(state);
    setStateSearch('');
    setIsStateSelectOpen(false);
  };

  const clearStateSearch = () => setStateSearch('');

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-lg space-y-6">
        {/* Header */}
        <div className="space-y-2 text-center">
          <div className="mb-4 flex items-center justify-center">
            <div className="rounded-full p-3">
              <Globe className="h-8 w-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Jurisdiction Information</h1>
          <p className="text-muted-foreground">
            Please select your country and state/province to continue
          </p>
        </div>

        {/* Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Details
            </CardTitle>
            <CardDescription>
              This information helps us provide region-specific services and compliance
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Country */}
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <CountryDropdown
                defaultValue={selectedCountry?.alpha3 || selectedCountry?.iso3}
                onChange={handleUICountrySelect}
                placeholder="Select a country"
                disabled={loadingCountries}
              />
            </div>

            {/* State/Province */}
            <div className="space-y-2">
              <Label htmlFor="state">{stateLabel}</Label>
              <Select
                value={selectedState?.id.toString() ?? ''}
                onValueChange={handleStateSelect}
                disabled={!selectedCountry || loadingStates}
                open={isStateSelectOpen}
                onOpenChange={setIsStateSelectOpen}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      !selectedCountry
                        ? 'Please select a country first'
                        : loadingStates
                          ? 'Loading...'
                          : `Select ${stateLabel.toLowerCase()}`
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {/* Search */}
                  {selectedCountry && !loadingStates && (
                    <div className="flex items-center border-b px-3 pb-2">
                      <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                      <div className="relative flex-1">
                        <input
                          type="text"
                          placeholder={`Search ${stateLabel.toLowerCase()}...`}
                          value={stateSearch}
                          onChange={(e) => setStateSearch(e.target.value)}
                          className="placeholder:text-muted-foreground h-8 w-full border-0 bg-transparent p-0 text-sm outline-none focus:ring-0"
                          onClick={(e) => e.stopPropagation()}
                          onKeyDown={(e) => e.stopPropagation()}
                          autoComplete="off"
                        />
                        {stateSearch && (
                          <button
                            type="button"
                            className="hover:bg-muted absolute top-0 right-0 flex h-8 w-8 items-center justify-center rounded-sm p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              clearStateSearch();
                            }}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* States List */}
                  <div className="max-h-48 overflow-y-auto">
                    {filteredStates.length > 0 ? (
                      filteredStates.map((state) => (
                        <SelectItem key={state.id} value={state.id.toString()}>
                          {state.name}
                        </SelectItem>
                      ))
                    ) : selectedCountry && !loadingStates ? (
                      <div className="text-muted-foreground px-3 py-2 text-sm">
                        No {stateLabel.toLowerCase()} found
                      </div>
                    ) : null}
                  </div>
                </SelectContent>
              </Select>
            </div>

            {/* Selected Values */}
            {(selectedCountry || selectedState) && (
              <div className="rounded-lg border p-4">
                <h3 className="mb-3 flex items-center gap-2 text-sm font-semibold">
                  <MapPin className="h-4 w-4" />
                  Selected Jurisdiction
                </h3>
                <div className="space-y-2">
                  {selectedCountry && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-sm">Country:</span>
                      <Badge variant="secondary">
                        <span className="mr-1">{selectedCountry.emoji}</span>
                        {selectedCountry.name}
                      </Badge>
                    </div>
                  )}
                  {selectedState && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-sm">{stateLabel}:</span>
                      <Badge variant="secondary">{selectedState.name}</Badge>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end">
          <Button
            onClick={onJurisdictionSubmit}
            disabled={!validateForm()}
            className="flex items-center space-x-2 rounded-lg px-8 py-3 font-medium transition-all"
          >
            <span>Continue</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Jurisdiction;
