'use client';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { FormioFormRenderer, FormioSchema } from '@/utils/formio';
import { But<PERSON>, Card, CardContent } from '@kratex-tradetech/kratex-ui-library';
import { FileText, Loader2 } from 'lucide-react';
import { useAccountBasics } from './useAccountBasics';

const DynamicAccountBasics = ({ formSchema }: { formSchema: FormioSchema | undefined }) => {
  const { isSubmitting, schemaLoading, isButtonDisabled, handleFormSubmit, isEmailVerified } =
    useAccountBasics();

  const form = useDynamicZodForm({
    formioJson: formSchema!,
  });

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-3xl space-y-6">
        {/* Header */}
        <div className="space-y-2 text-center">
          <div className="mb-4 flex justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full border">
              <FileText className="h-8 w-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Account Basics</h1>
          <p className="text-lg">Complete your account setup with required information</p>
        </div>
        <Card className="border shadow">
          {schemaLoading ? (
            <div className="flex items-center justify-center gap-2 p-6">
              <Loader2 className="h-4 w-4 animate-spin" />
              Loading your form
            </div>
          ) : (
            <CardContent className="p-6">
              {formSchema && (
                <FormioFormRenderer
                  form={form}
                  formioJson={formSchema}
                  onSubmit={handleFormSubmit}
                  showCard={false}
                >
                  <Button disabled={isButtonDisabled} type="submit" className="w-full">
                    Submit
                  </Button>
                  {!isEmailVerified && (
                    <div className="text-muted-foreground text-center text-sm">
                      Please enter your details and verify your email address to enable form
                      submission
                    </div>
                  )}
                </FormioFormRenderer>
              )}

              {isSubmitting && (
                <div className="mt-6 flex items-center justify-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Creating Account...</span>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
};

export const FormSchemaInitializer = () => {
  const { formSchema } = useAccountBasics();

  return formSchema && <DynamicAccountBasics formSchema={formSchema} />;
};

export default FormSchemaInitializer;
