'use client';

import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { userStore } from '@/store/user.store';
import { injectPhoneNumberIntoSchema } from '@/utils/formio/formSchemaDataInjection';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { AxiosError } from 'axios';
import { useBuyerRegister } from '@/services/buyer/useRegisterBuyer';
import { BuyerRegistrationPayload, BuyerRegistrationResponse } from './types';

export const useAccountBasics = () => {
  const router = useRouter();
  const { updateUserData, updateBuyerOnboardingData, userData, buyerOnboardingData } = userStore();
  const {
    data: formSchema,
    isLoading: schemaLoading,
    error: schemaError,
  } = useGetFormDefinition('accountBasicsBuyer');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutate: registerBuyer, isPending } = useBuyerRegister({
    onSuccess: handleSubmitSuccess,
    onSettled: handleSubmitSettled,
    onError: handleSubmitError,
  });

  // Transform the schema to make phone field disabled and pre-populated
  const transformedSchema = useMemo(() => {
    return formSchema?.schema && userData
      ? injectPhoneNumberIntoSchema(formSchema.schema, userData?.phone)
      : undefined;
  }, [formSchema?.schema, userData]);

  const handleFormSubmit = async (formData: Record<string, unknown>) => {
    const formDataWithPhone = {
      ...formData,
      phone: userData?.phone,
    };

    setIsSubmitting(true);

    if (!userData || !userData?.isEmailVerified) {
      toast.error('Please verify your email address before submitting the form.');
      setIsSubmitting(false);
      return;
    }
    if (!buyerOnboardingData) {
      toast.error('Please complete the previous steps before proceeding.');
      setIsSubmitting(false);
      return;
    }
    const payload: BuyerRegistrationPayload = {
      organisation_type: buyerOnboardingData.orgType.id,
      jurisdiction: buyerOnboardingData.jurisdiction.state.id,
      email: formData.email as string,
      name: formData.display_name as string,
      buyer_details: formDataWithPhone,
    };
    registerBuyer(payload);
    // TODO: discuss with backend and update name in response and remove this
    updateUserData({
      name: formData.display_name as string,
    });
    updateBuyerOnboardingData({
      accountBasics: { ...payload },
    });
  };

  function handleSubmitSuccess(data: BuyerRegistrationResponse) {
    toast.success('Account created successfully!');
    updateUserData({
      email: data.email,
      phone: data.phone,
    });
    router.push(`/buyer/kyc/`);
  }

  function handleSubmitError(error: AxiosError) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred.';
    toast.error(message);
  }

  function handleSubmitSettled() {
    setIsSubmitting(false);
  }

  return {
    formSchema: transformedSchema,
    isEmailVerified: userData?.isEmailVerified,
    schemaLoading,
    schemaError,
    isSubmitting,
    handleFormSubmit,
    isButtonDisabled: isPending,
  };
};
