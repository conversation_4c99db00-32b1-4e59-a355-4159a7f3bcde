'use client';

import { useGetOrganisationTypes } from '@/services/auth/useGetOrganisationType';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { OrganisationTypeOption } from './type';
import { userStore } from '@/store/user.store';

export const useOrganisationType = () => {
  const router = useRouter();
  const { userData, buyerOnboardingData, updateBuyerOnboardingData } = userStore();
  const [selectedType, setSelectedType] = useState<string>('');

  // Fetch org types via API
  const {
    data: organisationTypes = [],
    isLoading,
    error,
  } = useGetOrganisationTypes(
    buyerOnboardingData?.jurisdiction?.country?.iso2 || '',
    String(userData?.user_type), // role (number not the label)
  );

  const proceed = () => {
    if (selectedType) {
      const selectedOrg = organisationTypes.find(
        (type: OrganisationTypeOption) => type.name === selectedType,
      );
      if (selectedOrg) {
        updateBuyerOnboardingData({ orgType: { id: selectedOrg.id, name: selectedOrg.name } });
      }
      router.push('/buyer/account-basics');
    }
  };

  return {
    isLoading,
    selectedType,
    setSelectedType,
    organisationTypes,
    proceed,
    error,
  };
};
