'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  RadioGroup,
  RadioGroupItem,
} from '@kratex-tradetech/kratex-ui-library';
import { ChevronRight, Loader2 } from 'lucide-react';
import { useOrganisationType } from '@/components/buyer/onboarding/org-type/useOrganisationType';
import { OrganisationTypeOption } from '@/components/buyer/onboarding/org-type/type';

const SpecifyOrganisationType = () => {
  const { selectedType, organisationTypes, isLoading, setSelectedType, proceed } =
    useOrganisationType();

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-8">
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Specify Organisation Type</h1>
          <p className="text-muted-foreground text-lg">
            Please select the type of organisation you represent to tailor your experience
          </p>
        </div>

        {/* Options Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Organisation Details</CardTitle>
            <CardDescription>
              Choose the option that best describes your organisation
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                <p>Loading organisation types...</p>
              </div>
            ) : (
              <RadioGroup
                value={selectedType}
                onValueChange={setSelectedType}
                className="space-y-4"
              >
                {organisationTypes.map((type: OrganisationTypeOption) => (
                  <div
                    key={type.name}
                    className={`flex cursor-pointer items-start space-x-3 rounded-lg border-2 p-3 transition-all ${
                      selectedType === type.name
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedType(type.name)}
                  >
                    <RadioGroupItem value={type.name} id={type.name} className="mt-1" />
                    <div className="flex-1 space-y-1">
                      <Label htmlFor={type.name} className="cursor-pointer text-base font-medium">
                        {type.name}
                      </Label>
                    </div>
                  </div>
                ))}
              </RadioGroup>
            )}
          </CardContent>
        </Card>

        {/* Proceed Button */}
        <div className="flex justify-end">
          <Button
            onClick={proceed}
            disabled={!selectedType}
            className="flex items-center space-x-2 px-8 py-3"
          >
            <span>Proceed</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SpecifyOrganisationType;
