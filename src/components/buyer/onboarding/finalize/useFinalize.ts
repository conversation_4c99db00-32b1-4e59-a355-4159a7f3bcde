'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFinalizeOnboarding } from '@/services/auth/useFinalizeOnboarding';

export const useReviewOnboarding = () => {
  const router = useRouter();
  const { mutate: finalizeSubmit, isPending } = useFinalizeOnboarding({
    onSuccess(data) {
      toast.success(data.message);
      router.push('/buyer/dashboard');
    },
    onError: () => {
      toast.error('Failed to finalise onboarding, Please try again!');
    },
  });

  const handleConfirmFinish = () => finalizeSubmit('buyer');

  return {
    isSubmitting: isPending,
    handleConfirmFinish,
  };
};
