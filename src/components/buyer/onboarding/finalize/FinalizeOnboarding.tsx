'use client';

import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Separator,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle } from 'lucide-react';
import { useReviewOnboarding } from './useFinalize';

// Component imports
import KycStatus from '../../kyc-management/kyc-status/KycStatus';
import SourcingIntentDisplayContainer from '../../sourcing-intent-management/display-and-edit/SourcingIntentDisplayContainer';
import TeamMembersDisplayContainer from '../../team-members-management/display-and-add/TeamMembersDisplayContainer';
import BasicInformationDisplayContainer from '../../basic-information-management/dispaly-and-edit/BasicInformationDisplayContainer';

const ReviewOnboarding = () => {
  const { isSubmitting, handleConfirmFinish } = useReviewOnboarding();
  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto max-w-4xl">
        <Card>
          {/* Header */}
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Review and Finalize
            </CardTitle>
            <CardDescription>
              Please review all the information you have provided before finalizing your account
              setup.
            </CardDescription>
          </CardHeader>

          {/* Content */}
          <CardContent className="space-y-6">
            <BasicInformationDisplayContainer title="Basic Information" showEditButton={false} />
            <KycStatus />
            <SourcingIntentDisplayContainer userTypeLabel="buyer" showEditButton={true} />
            <TeamMembersDisplayContainer title="Team Members" showAddButton={false} />
            <Separator />

            {/* Confirm Button */}
            <div className="flex justify-center pt-4">
              <Button
                onClick={handleConfirmFinish}
                disabled={isSubmitting}
                size="lg"
                className="min-w-48"
              >
                {isSubmitting ? 'Creating Account...' : 'Confirm & Finish'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ReviewOnboarding;
