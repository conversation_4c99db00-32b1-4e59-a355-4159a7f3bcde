import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  Card<PERSON>ontent,
  Badge,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle, Loader2 } from 'lucide-react';
import useKycStatus from './useKycStatus';

const KycStatus = () => {
  const { isLoading, kycStatus, data } = useKycStatus();
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CheckCircle className="h-4 w-4" />
          KYC Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2">
          {isLoading ? (
            <Loader2 className="animate-spin" />
          ) : (
            <>
              <Badge variant={kycStatus === 'Active' ? 'default' : 'secondary'}>{kycStatus}</Badge>
              <p className="text-sm text-gray-600">
                {data?.status
                  ? kycStatus === 'Active'
                    ? 'Your account will be activated immediately'
                    : 'Your account will be pending KYC verification'
                  : ''}
              </p>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default KycStatus;
