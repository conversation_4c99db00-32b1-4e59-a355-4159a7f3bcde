import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { useGetKycStatus } from '@/services/kyc/useGetKycStatus';
import { getKycStatus, kycFormKeyMap } from '@/utils/kyc';
import { useEffect, useMemo } from 'react';
import { toast } from 'sonner';

const useKycStatus = () => {
  const { data: userData } = useGetUserInfo('buyer');
  const { data, isLoading, isError, isSuccess } = useGetKycStatus({
    keyword: userData?.organisation_type?.id ? kycFormKeyMap[userData?.organisation_type?.id] : '',
    userTypeLabel: 'buyer',
  });

  const kycStatus = useMemo(
    () =>
      isSuccess && data
        ? getKycStatus(data?.status)
        : 'Error fetching KYC status. Please try again later.',
    [isSuccess, data],
  );

  useEffect(() => {
    if (isError) {
      toast.error('Error fetching KYC status! Please try again later.');
    }
  }, [isError]);
  return { isLoading, kycStatus, data };
};

export default useKycStatus;
