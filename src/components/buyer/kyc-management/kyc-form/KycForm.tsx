'use client';

import { LoaderCircle } from 'lucide-react';

import GenericFormRenderer from '@/components/common/GenericFormRenderer';
import BuyingAgentIndia from '@/components/buyer/onboarding/kyc/buyingAgentIndia/buyingAgentIndia';
import GovernmentBodyIndia from '@/components/buyer/onboarding/kyc/governmentBody/governmentBody';
import IndividualIndia from '@/components/buyer/onboarding/kyc/individualIndia/IndividualIndia';
import NonProfitIndia from '@/components/buyer/onboarding/kyc/nonProfitIndia/nonProfitIndia';
import RegisteredCompanyIndia from '@/components/buyer/onboarding/kyc/registered-company-india/RegisteredCompanyIndia';
import SolePropriterIndia from '@/components/buyer/onboarding/kyc/soleProprietorIndia/SoleProprietorIndia';
import useKycForm from './useKycForm';

const KycForm = () => {
  const { buyerInfoData, isGetBuyerInfoLoading } = useKycForm();
  const orgType = buyerInfoData?.organisation_type;
  const orgId = orgType?.id || 0;

  if (isGetBuyerInfoLoading) {
    return <LoaderCircle className="animate-spin" />;
  }

  return (
    <>
      {orgId === 1 && <IndividualIndia />}
      {orgId === 2 && <SolePropriterIndia />}
      {orgId === 3 && <RegisteredCompanyIndia />}
      {orgId === 4 && <GovernmentBodyIndia />}
      {orgId === 5 && <NonProfitIndia />}
      {orgId === 6 && <BuyingAgentIndia />}

      {[1, 2, 3, 4, 5, 6].includes(orgId) || (
        <GenericFormRenderer orgId={orgId || 0} orgName={orgType?.name || ''} />
      )}
    </>
  );
};

export default KycForm;
