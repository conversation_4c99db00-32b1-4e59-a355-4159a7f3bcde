'use client';

import { Search, Bell, LogOut, User } from 'lucide-react';
import {
  Avatar,
  AvatarFallback,
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import Link from 'next/link';
import useLogout from '@/hooks/useLogout';
import { getUserInitials } from '@/utils/getUserInitials';
import { userStore } from '@/store/user.store';

export default function Header() {
  const { userData } = userStore();
  const { handleLogout } = useLogout();

  if (!userData) return <></>;

  return (
    <header className="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6">
      <div className="flex items-center gap-4">
        <Link href={'/buyer/dashboard'}>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded bg-black">
              <span className="text-sm font-bold text-white">K</span>
            </div>
            <span className="font-semibold text-gray-900">KrateX</span>
          </div>
        </Link>
      </div>

      <div className="mx-8 max-w-md flex-1">
        <div className="relative">
          <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
          <Input placeholder="Search..." className="border-gray-200 bg-gray-50 pl-10" />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" className="relative cursor-pointer">
          <Bell className="h-5 w-5 text-gray-600" />
          <Badge className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center bg-black p-0 text-xs text-white">
            2
          </Badge>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className="cursor-pointer" variant="ghost" size="sm">
              <Avatar
                title="Dummy User"
                className="hover:ring-primary cursor-pointer transition-all hover:ring-2 hover:ring-offset-2"
                onClick={() => {}}
              >
                <AvatarFallback>{getUserInitials(userData?.name)}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem className="px-3">
              <User />
              <div className="border-b border-gray-100">
                <p className="text-sm font-medium text-gray-900">{userData?.name}</p>
                <p className="text-xs text-gray-500">{userData?.email}</p>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem className="px-3">
              <button
                onClick={handleLogout}
                className="flex w-full cursor-pointer items-center gap-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
