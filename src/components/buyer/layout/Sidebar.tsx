'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { navigation } from './constants';

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="h-full w-64 border-r border-gray-200 bg-white">
      <nav className="mt-6 px-3">
        <div className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname.startsWith(item.href);
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`'group transition-colors', flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${
                  isActive
                    ? 'border-r-2 border-gray-900 bg-gray-100 text-gray-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                } `}
              >
                <item.icon
                  className={`'mr-3 flex-shrink-0', h-5 w-5 ${
                    isActive ? 'text-gray-900' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
              </Link>
            );
          })}
        </div>
      </nav>
    </div>
  );
}
