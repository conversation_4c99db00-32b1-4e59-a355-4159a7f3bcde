import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useGetInvitedTeamMembers } from '@/services/useGetInvitedTeam';
import { EditTeamValues } from '../edit-team-member/useEditTeamMember';
import { Data } from '../invite-team/types';
import { useDeleteUser } from '@/services/auth/useDeleteUser';
import { toast } from 'sonner';

const defaultContactValues = {
  id: 0,
  full_name: '',
  phone_number: '',
  email: '',
  role: 'PROCUREMENT',
};

const useTeamMembersManagement = () => {
  const queryClient = useQueryClient();
  const [selectedContact, setSelectedContact] = useState<EditTeamValues>(defaultContactValues);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  // TODO PSG: replace this with users/team API (waiting for BE changes)
  const {
    data: teamMembersData,
    isError,
    isLoading,
    isSuccess,
  } = useGetInvitedTeamMembers('buyer');

  const { mutate: deleteUser, isPending: isDeleting } = useDeleteUser({
    onSuccess(data) {
      queryClient.invalidateQueries({
        // TODO PSG: update key as per GET API (users/team)
        queryKey: ['invited-team-members', 'buyer'],
      });
      toast.success(data.message || 'User deleted successfully!');
    },
    onError: () => {
      toast.error('Failed to delete user!');
    },
  });

  // Modal handlers
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  const openUpdateModal = () => setIsUpdateModalOpen(true);
  const closeUpdateModal = () => setIsUpdateModalOpen(false);

  const onEdit = (contact: Data) => {
    setSelectedContact({
      id: contact.id,
      full_name: contact.name,
      email: contact.email,
      // TODO PSG: handle this (waiting for BE changes)
      role: 'PROCUREMENT',
      phone_number: contact.phone,
    });
    openUpdateModal();
  };

  const onDelete = (id: number) => {
    deleteUser(id);
  };

  return {
    displayData: teamMembersData,

    isLoading,
    isError,
    isSuccess,
    isDeleting,

    selectedContact,

    // Modal states
    isModalOpen,
    isUpdateModalOpen,
    openModal,
    closeModal,
    closeUpdateModal,
    onEdit,
    onDelete,
  };
};

export default useTeamMembersManagement;
