'use client';

import { FC, ReactNode } from 'react';

import TeamMembersDisplay from './TeamMembersDisplay';
import useTeamMembersManagement from './useTeamMembersManagement';
import AddTeamMembersModal from './AddTeamMembersModal';
import EditTeamMemberModal from '../edit-team-member/EditTeamMemberModal';

interface TeamMembersDisplayContainerProps {
  userTypeLabel?: 'buyer' | 'seller';
  showAddButton?: boolean;
  isProfileManagement?: boolean;
  title?: string;
  icon?: ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
  onUpdateSuccess?: () => void;
  onUpdateError?: (error: any) => void;
}

const TeamMembersDisplayContainer: FC<TeamMembersDisplayContainerProps> = ({
  showAddButton = true,
  title,
  icon,
  className,
  isProfileManagement = false,
  variant = 'card',
}) => {
  const {
    displayData,
    isLoading,
    isModalOpen,
    isDeleting,
    isUpdateModalOpen,
    selectedContact,
    closeUpdateModal,
    openModal,
    closeModal,
    onEdit,
    onDelete,
  } = useTeamMembersManagement();
  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-48 rounded-lg bg-gray-200"></div>
      </div>
    );
  }

  return (
    <>
      <TeamMembersDisplay
        data={displayData}
        onAdd={showAddButton ? openModal : undefined}
        showAddButton={showAddButton}
        isDeleting={isDeleting}
        isProfileManagement={isProfileManagement}
        title={title}
        icon={icon}
        className={className}
        variant={variant}
        onDelete={onDelete}
        onEdit={onEdit}
      />

      {showAddButton && <AddTeamMembersModal isOpen={isModalOpen} onClose={closeModal} />}

      <EditTeamMemberModal
        selectedContact={selectedContact}
        isOpen={isUpdateModalOpen}
        onClose={closeUpdateModal}
      />
    </>
  );
};

export default TeamMembersDisplayContainer;
