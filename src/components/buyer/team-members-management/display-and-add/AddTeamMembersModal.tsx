import { FC } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@kratex-tradetech/kratex-ui-library';

import InviteTeam from '../invite-team/InviteTeam';

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const AddTeamMembersModal: FC<Props> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-4xl min-w-3xl overflow-auto">
        <DialogHeader>
          <DialogTitle>Add Team Members</DialogTitle>
        </DialogHeader>
        <InviteTeam successCallback={onClose} showSkip={false} />
      </DialogContent>
    </Dialog>
  );
};

export default AddTeamMembersModal;
