import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kratex-tradetech/kratex-ui-library';
import { Loader2Icon, MoreHorizontal, User } from 'lucide-react';

import { Data, InvitedTeamResponse } from '../invite-team/types';
import { ReactNode } from 'react';
import { StatusType, UserStatus } from '@/components/reusable/UserStatus';

interface TeamMembersDisplayProps {
  data?: InvitedTeamResponse;
  isProfileManagement?: boolean;
  isSuccess?: boolean;
  isLoading?: boolean;
  isError?: boolean;
  isDeleting?: boolean;
  showAddButton?: boolean;
  title?: string;
  icon?: ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
  onAdd?: () => void;
  onEdit?: (contact: Data) => void;
  onDelete?: (id: number) => void;
}

const TeamMembersDisplay = ({
  data,
  className,
  icon,
  showAddButton,
  title,
  isProfileManagement = false,
  isDeleting,
  onAdd,
  onEdit,
  onDelete,
}: TeamMembersDisplayProps) => {
  const statuses = ['invited', 'active', 'archived'] as StatusType[];
  const teamMembers = data?.data || [];
  const teamMembersCount = data && data?.data.length > 0 ? `(${data?.data.length})` : '';
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            <h3 className="text-lg font-medium text-gray-900">
              {title} {teamMembersCount}
            </h3>
          </CardTitle>
          {showAddButton && onAdd && (
            <Button className="cursor-pointer" variant="outline" size="sm" onClick={onAdd}>
              <User className="mr-1 h-3 w-3" />
              Add Team Members
            </Button>
          )}
        </div>
      </CardHeader>
      {!teamMembers?.length ? (
        <div className="py-8 text-center">
          <p className="text-gray-500">No team members found</p>
        </div>
      ) : (
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email Address</TableHead>
                  <TableHead>Phone Number</TableHead>
                  {isProfileManagement && (
                    <>
                      <TableHead>Role</TableHead>
                      <TableHead>Invited By</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </>
                  )}
                </TableRow>
              </TableHeader>

              <TableBody>
                {teamMembers?.map((contact) => (
                  <TableRow key={contact.id}>
                    <TableCell>{contact.name}</TableCell>
                    <TableCell className="text-blue-600">{contact.email}</TableCell>
                    <TableCell>{contact.phone}</TableCell>
                    {isProfileManagement && (
                      <>
                        <TableCell>Role</TableCell>
                        <TableCell>{contact.invited_by?.email}</TableCell>
                        <TableCell>
                          <UserStatus
                            status={statuses[Math.floor(Math.random() * statuses.length)]}
                          />
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button className="cursor-pointer" variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                className="cursor-pointer"
                                onClick={() => onEdit?.(contact)}
                              >
                                Edit Contact
                              </DropdownMenuItem>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <div className="hover:bg-accent cursor-pointer rounded-md px-[8px] py-[6px] text-[14px] text-red-600">
                                    Archive Contact
                                  </div>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel className="cursor-pointer">
                                      Cancel
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      disabled={isDeleting}
                                      className="cursor-pointer"
                                      onClick={() => onDelete?.(contact.id)}
                                    >
                                      {isDeleting && <Loader2Icon className="animate-spin" />}
                                      Continue
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default TeamMembersDisplay;
