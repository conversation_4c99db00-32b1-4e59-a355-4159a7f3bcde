'use client';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  PhoneInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';
import { Loader2Icon, Plus, TrashIcon, UserPlus } from 'lucide-react';
import { useBuyerInviteTeam } from './useInviteTeam';
// import { withBuyerRoute } from '@/utils/withRoleAccess';
import { seatRoleOptions } from './types';

const InviteTeamMembers = ({
  successCallback,
  showSkip = true,
}: {
  successCallback?: () => void;
  showSkip?: boolean;
}) => {
  const { fields, isButtonDisabled, form, isLoading, append, remove, onSubmit, handleSkip } =
    useBuyerInviteTeam({ successCallback });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex w-full flex-col items-center">
        <Card className="w-full max-w-4xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Invite Team Members
            </CardTitle>
            <CardDescription>
              Add team members to your account. This step is optional and can be completed later.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {fields.map((field, index) => (
              <Card key={field.id} className="w-full">
                <CardHeader>
                  <CardTitle>Team Member {index + 1}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-x-4">
                    <FormField
                      control={form.control}
                      name={`teams.${index}.full_name`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="John Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`teams.${index}.phone_number`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <PhoneInput
                              value={field.value}
                              onChange={field.onChange}
                              defaultCountry="IN" // for fallback
                              international
                              countryCallingCodeEditable={false}
                              placeholder="Enter phone number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex gap-x-4">
                    <FormField
                      control={form.control}
                      name={`teams.${index}.email`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`teams.${index}.role`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Seat Role</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {seatRoleOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:bg-red-600 hover:text-white"
                    disabled={fields.length === 1}
                  >
                    <TrashIcon /> Remove Member
                  </Button>
                </CardFooter>
              </Card>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                append({ full_name: '', phone_number: '', email: '', role: 'PROCUREMENT' })
              }
              className="mt-2 w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Another Team Member
            </Button>
          </CardContent>
          <CardFooter className="!m-0 flex flex-col gap-3 sm:flex-row">
            <Button type="submit" className="flex-1 cursor-pointer" disabled={isLoading}>
              {isLoading && <Loader2Icon className="animate-spin" />}
              Send Invitations
            </Button>
            {showSkip && (
              <Button
                type="button"
                variant="outline"
                disabled={isButtonDisabled}
                onClick={handleSkip}
                className="flex-1 cursor-pointer"
              >
                Skip for Now
              </Button>
            )}
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default InviteTeamMembers;
