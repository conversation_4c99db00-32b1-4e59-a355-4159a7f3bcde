import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { useInviteTeam } from '@/services/auth/useInviteTeam';
import z from 'zod';
import { seatRoleOptions } from './types';
import { toast } from 'sonner';
import { usePathname, useRouter } from 'next/navigation';
import { useSkip } from '@/services/forms/useSkip';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { useQueryClient } from '@tanstack/react-query';

const userTypeEnumValues = seatRoleOptions.map((opt) => opt.value) as [string, ...string[]];

const FormSchema = z.object({
  teams: z
    .array(
      z.object({
        full_name: z.string().min(2, {
          message: 'Name must be at least 2 characters.',
        }),
        phone_number: z
          .string()
          .min(1, 'Phone number is required')
          .refine((val) => isValidPhoneNumber(val), {
            message: 'Invalid phone number',
          }),

        email: z.string().email('Invalid email'),

        role: z.enum(userTypeEnumValues, {
          errorMap: () => ({ message: 'Please select a valid seat role' }),
        }),
      }),
    )
    .min(1, 'You have to invite at least one team member'),
});

type FormValues = z.infer<typeof FormSchema>;

export const useBuyerInviteTeam = ({ successCallback }: { successCallback?: () => void }) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const pathname = usePathname();
  const { mutate: skipInvite, isPending } = useSkip({
    onSuccess() {
      toast.info('Skipped Invited team. You can invite team members later from profile.');
      if (pathname !== '/buyer/profile-management') router.push('/buyer/finalize-onboarding');
    },
    onError() {
      toast.error('Skipping failed. Please try again');
    },
  });
  const { mutate: mutation, isPending: isInviting } = useInviteTeam({
    onSuccess: () => {
      toast.success('Team invited successfully!');
      queryClient.invalidateQueries({
        queryKey: ['invited-team-members', 'buyer'],
      });
      successCallback?.();
      if (pathname !== '/buyer/profile-management') router.push('/buyer/finalize-onboarding');
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to invite team.');
    },
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      teams: [
        {
          full_name: '',
          phone_number: '',
          email: '',
          role: 'PROCUREMENT',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'teams',
  });

  function onSubmit(data: FormValues) {
    mutation({
      userTypeLabel: 'buyer',
      payload: data,
    });
  }

  return {
    form,
    fields,
    isButtonDisabled: isPending,
    isLoading: isInviting,
    onSubmit,
    append,
    remove,
    handleSkip: () => skipInvite({ state: 'mark_invite_member_skipped' }),
  };
};
