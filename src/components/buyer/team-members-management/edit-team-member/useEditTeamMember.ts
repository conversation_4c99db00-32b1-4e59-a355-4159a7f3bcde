import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import z from 'zod';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { toast } from 'sonner';
import { seatRoleOptions } from '../invite-team/types';
import { useUpdateUserRole } from '@/services/auth/useUpdateUserRole';
import { useQueryClient } from '@tanstack/react-query';

const userTypeEnumValues = seatRoleOptions.map((opt) => opt.value) as [string, ...string[]];

const EditTeamSchema = z.object({
  full_name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  phone_number: z
    .string()
    .min(1, 'Phone number is required')
    .refine((val) => isValidPhoneNumber(val), {
      message: 'Invalid phone number',
    }),
  email: z.string().email('Invalid email'),
  role: z.enum(userTypeEnumValues, {
    errorMap: () => ({ message: 'Please select a valid seat role' }),
  }),
  id: z.number(),
});

export type EditTeamValues = z.infer<typeof EditTeamSchema>;

export const useEditTeamMember = ({
  defaultValues,
  onSuccess,
}: {
  defaultValues: EditTeamValues;
  onSuccess?: () => void;
}) => {
  const queryClient = useQueryClient();
  const { mutate: updateUserRole, isPending: isUpdating } = useUpdateUserRole({
    onSuccess(data) {
      queryClient.invalidateQueries({
        // TODO PSG: update key as per GET API (users/team)
        queryKey: ['invited-team-members', 'buyer'],
      });
      toast.success(data.message || 'User role updated successfully!');
      onSuccess?.();
    },
    onError: () => {
      toast.error('Failed to update user role!');
    },
  });

  const form = useForm<EditTeamValues>({
    resolver: zodResolver(EditTeamSchema),
    defaultValues,
  });

  const handleSubmit = form.handleSubmit(async (values) => {
    updateUserRole({ id: values.id, role: values.role });
  });

  return {
    form,
    handleSubmit,
    isLoading: isUpdating,
  };
};
