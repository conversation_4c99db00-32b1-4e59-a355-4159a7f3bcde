'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardFooter,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  PhoneInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';

import { Loader2Icon } from 'lucide-react';
import { EditTeamValues, useEditTeamMember } from './useEditTeamMember';
import { seatRoleOptions } from '../invite-team/types';

export const EditTeamMember = ({
  defaultValues,
  onSuccess,
}: {
  defaultValues: EditTeamValues;
  onSuccess?: () => void;
}) => {
  const { form, handleSubmit, isLoading } = useEditTeamMember({ defaultValues, onSuccess });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex w-full flex-col items-center">
        <Card className="w-full max-w-3xl">
          <CardContent className="space-y-4">
            <div className="flex gap-x-4">
              <FormField
                control={form.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input disabled placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone_number"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <PhoneInput
                        disabled
                        value={field.value}
                        onChange={field.onChange}
                        defaultCountry="IN"
                        international
                        countryCallingCodeEditable={false}
                        placeholder="Enter phone number"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-x-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input disabled type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Seat Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {seatRoleOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-end">
            <Button type="submit" className="mx-auto w-fit cursor-pointer" disabled={isLoading}>
              {isLoading && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
              Update Contact
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};
