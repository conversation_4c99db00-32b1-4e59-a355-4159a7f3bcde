import { FC } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kratex-tradetech/kratex-ui-library';
import { EditTeamMember } from './EditTeamMember';
import { EditTeamValues } from './useEditTeamMember';

interface Props {
  selectedContact: EditTeamValues;
  isOpen: boolean;
  onClose: () => void;
}

const EditTeamMemberModal: FC<Props> = ({ isOpen, selectedContact, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-4xl min-w-3xl overflow-auto">
        <DialogHeader>
          <DialogTitle>Edit Team Member</DialogTitle>
        </DialogHeader>
        <EditTeamMember defaultValues={selectedContact} onSuccess={onClose} />
      </DialogContent>
    </Dialog>
  );
};

export default EditTeamMemberModal;
