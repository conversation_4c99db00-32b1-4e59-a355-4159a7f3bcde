import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';

export interface SourcingIntentData {
  sourcing_for: string[];
  sourcing_from: string[];
  use_case: string[];
  use_case_enum?: number[];
  pref_shipping_term: string;
  pref_shipping_term_enum?: number;
  target_lead_time: string;
  target_lead_time_enum?: number;
  product_categories: string[];
  compliance_reqs: string[];
  compliance_reqs_enum?: number[];
}

export interface SourcingIntentFormData {
  sourcing_for: DropdownOption[];
  sourcing_from: DropdownOption[];
  use_case: DropdownOption[];
  product_categories: string[];
  compliance_reqs: DropdownOption[];
  pref_shipping_term: DropdownOption;
  target_lead_time: DropdownOption[];
}

export interface UseSourcingIntentOptions {
  userTypeLabel?: 'buyer' | 'seller';
  onUpdateSuccess?: () => void;
  onUpdateError?: (error: any) => void;
}
