import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kratex-tradetech/kratex-ui-library';
import SourcingIntentForm from '@/components/buyer/sourcing-intent-management/save-preferences/form/SourcingIntentForm';
import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';
import {
  fetchUseCases,
  fetchProductCategories,
  fetchComplianceOptions,
  fetchShippingTerms,
  fetchLeadTimeOptions,
} from '@/components/buyer/sourcing-intent-management/save-preferences/useSourcingIntent';
import { useCountries } from '@/services/geolocation/useGetJurisdiction';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  formData: any;
  setFormData: React.Dispatch<React.SetStateAction<any>>;
  onSave: () => Promise<void>;
  NO_PREFERENCE_OPTION: { id: number; name: string };
  isLoading?: boolean;
}

const EditSourcingIntentModal: React.FC<Props> = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  onSave,
  NO_PREFERENCE_OPTION,
}) => {
  const { data: countries = [], isLoading: loadingCountries } = useCountries();

  const [useCases, setUseCases] = useState<DropdownOption[]>([]);
  const [productCategories, setProductCategories] = useState<string[]>([]);
  const [complianceOptions, setComplianceOptions] = useState<DropdownOption[]>([]);
  const [shippingTerms, setShippingTerms] = useState<DropdownOption[]>([]);
  const [leadTimeOptions, setLeadTimeOptions] = useState<DropdownOption[]>([]);

  useEffect(() => {
    (async () => {
      const [u, p, c, s, l] = await Promise.all([
        fetchUseCases(),
        fetchProductCategories(),
        fetchComplianceOptions(),
        fetchShippingTerms(),
        fetchLeadTimeOptions(),
      ]);
      setUseCases(u);
      setProductCategories(p);
      setComplianceOptions(c);
      setShippingTerms(s);
      setLeadTimeOptions(l);
    })();
  }, []);

  if (!formData) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-4xl min-w-3xl overflow-auto">
        <DialogHeader>
          <DialogTitle>Edit Sourcing Intent</DialogTitle>
        </DialogHeader>
        <SourcingIntentForm
          formData={formData}
          countries={countries}
          useCases={useCases}
          productCategories={productCategories}
          complianceOptions={complianceOptions}
          shippingTerms={shippingTerms}
          leadTimeOptions={leadTimeOptions}
          loadingCountries={loadingCountries}
          NO_PREFERENCE_OPTION={NO_PREFERENCE_OPTION}
          handleDropdownSelect={(field, option) => {
            setFormData((prev: any) => {
              const values = prev[field];
              const isSelected = values.some((item: any) => item.id === option.id);
              const updated = isSelected
                ? values.filter((item: any) => item.id !== option.id)
                : [...values.filter((item: any) => item.id !== -1), option];

              return {
                ...prev,
                [field]: option.id === -1 ? [option] : updated,
              };
            });
          }}
          handleCheckboxChange={(field, option, checked) => {
            setFormData((prev: any) => {
              const current = prev[field] || [];
              const updated = checked
                ? [...current, option]
                : current.filter((item: any) => item.id !== option.id);
              return {
                ...prev,
                [field]: updated,
              };
            });
          }}
          handleProductCategoryChange={(cat, checked) => {
            setFormData((prev: any) => ({
              ...prev,
              product_categories: checked
                ? [...prev.product_categories, cat]
                : prev.product_categories.filter((c: string) => c !== cat),
            }));
          }}
          handleShippingTermChange={(id, checked) => {
            const selected = shippingTerms.find((s) => s.id === id);
            setFormData((prev: any) => ({
              ...prev,
              pref_shipping_term: checked ? selected : NO_PREFERENCE_OPTION,
            }));
          }}
          handleLeadTimeSelect={(val) => {
            const selected = leadTimeOptions.find((t) => t.name === val);
            setFormData((prev: any) => ({
              ...prev,
              target_lead_time: selected ? [selected] : [],
            }));
          }}
          onSubmit={onSave}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EditSourcingIntentModal;
