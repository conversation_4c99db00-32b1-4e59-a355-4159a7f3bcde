'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useGetSourcingIntentInfo } from '@/services/sourcing-intent/useGetSourcingIntent';
import { useUpdateSourcingIntent } from '@/services/sourcing-intent/useUpdateSourcingIntent';
import { SourcingIntentData, SourcingIntentFormData, UseSourcingIntentOptions } from './types';

const NO_PREFERENCE_OPTION: DropdownOption = { id: -1, name: 'No preference' };

export const useSourcingIntentManagement = (options: UseSourcingIntentOptions = {}) => {
  const { userTypeLabel = 'buyer', onUpdateSuccess, onUpdateError } = options;

  const { data: sourcingData, isLoading: isSourcingLoading } =
    useGetSourcingIntentInfo(userTypeLabel);
  const { mutateAsync: updateSourcingIntent, isPending: isUpdating } =
    useUpdateSourcingIntent(userTypeLabel);

  const [editFormData, setEditFormData] = useState<SourcingIntentFormData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Transform API data to display format
  const mapSourcingIntentApiToData = (apiData: any): SourcingIntentData => {
    return {
      sourcing_for: (apiData.sourcing_for || []).map((item: any) => item.name),
      sourcing_from: (apiData.sourcing_from || []).map((item: any) => item.name),
      use_case: (apiData.use_case || []).map((item: any) => item.value),
      use_case_enum: (apiData.use_case || []).map((item: any) => item.enum),
      product_categories: apiData.product_categories ? apiData.product_categories.split(',') : [],
      compliance_reqs: (apiData.compliance_reqs || []).map((item: any) => item.value),
      compliance_reqs_enum: (apiData.compliance_reqs || []).map((item: any) => item.enum),
      pref_shipping_term: apiData.shipping_term?.value || '',
      pref_shipping_term_enum: apiData.shipping_term?.enum,
      target_lead_time: apiData.lead_time?.value || '',
      target_lead_time_enum: apiData.lead_time?.enum,
    };
  };

  // Get display data
  const displayData = sourcingData ? mapSourcingIntentApiToData(sourcingData) : null;

  // Initialize form data when sourcing data loads
  useEffect(() => {
    if (sourcingData) {
      const mapToOption = (item: any) =>
        item ? { id: item.enum ?? item.id, name: item.value ?? item.name } : null;

      setEditFormData({
        sourcing_for: sourcingData.sourcing_for || [],
        sourcing_from: sourcingData.sourcing_from?.length
          ? sourcingData.sourcing_from
          : [NO_PREFERENCE_OPTION],
        use_case: sourcingData.use_case?.map(mapToOption) || [],
        product_categories: sourcingData.product_categories
          ? sourcingData.product_categories.split(',').map((s: string) => s.trim())
          : [],
        compliance_reqs: sourcingData.compliance_reqs?.map(mapToOption) || [],
        pref_shipping_term: mapToOption(sourcingData.shipping_term) || NO_PREFERENCE_OPTION,
        target_lead_time: sourcingData.lead_time
          ? [{ id: sourcingData.lead_time.enum, name: sourcingData.lead_time.value }]
          : [],
      });
    }
  }, [sourcingData]);

  // Modal handlers
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Update handler
  const handleUpdate = async () => {
    if (!editFormData) return;

    const payload: SourcingIntentPayload = {
      sourcing_for: editFormData.sourcing_for.map((item: DropdownOption) => item.id),
      sourcing_from: editFormData.sourcing_from.map((item: DropdownOption) => item.id),
      use_cases: editFormData.use_case.map((item: DropdownOption) => item.id),
      compliance_reqs: editFormData.compliance_reqs.map((item: DropdownOption) => item.id),
      pref_shipping_term: editFormData.pref_shipping_term.id,
      target_lead_time: editFormData.target_lead_time?.[0]?.id ?? 0,
      product_categories: editFormData.product_categories.join(', '),
    };

    try {
      await updateSourcingIntent(payload);
      toast.success('Sourcing intent updated successfully.');
      closeModal();
      onUpdateSuccess?.();
    } catch (err: any) {
      console.error(err);
      toast.error('Failed to update sourcing intent.');
      onUpdateError?.(err);
    }
  };

  return {
    // Data
    displayData,
    formData: editFormData,
    setFormData: setEditFormData,

    // Loading states
    isLoading: isSourcingLoading,
    isUpdating,

    // Modal state
    isModalOpen,
    openModal,
    closeModal,

    // Actions
    handleUpdate,

    // Constants
    NO_PREFERENCE_OPTION,
  };
};
