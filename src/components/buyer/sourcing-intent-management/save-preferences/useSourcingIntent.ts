'use client';

import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';
import { useCountries } from '@/services/geolocation/useGetJurisdiction';
import { useCreateSourcingIntent } from '@/services/sourcing-intent/useCreateSourcingIntent';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { FormDataType } from './types';
import { AxiosError } from 'axios';

// Simulated API calls
export const fetchUseCases = async (): Promise<DropdownOption[]> =>
  new Promise((resolve) =>
    setTimeout(
      () =>
        resolve([
          { id: 1, name: 'Resale' },
          { id: 2, name: 'Own-manufacture' },
          { id: 3, name: 'R&D sample' },
          { id: 4, name: 'Dropship' },
          { id: 5, name: 'Govt project' },
        ]),
      300,
    ),
  );

export const fetchProductCategories = async (): Promise<string[]> =>
  new Promise((resolve) =>
    setTimeout(
      () =>
        resolve([
          'Electronics',
          'Automotive',
          'Industrial Machinery',
          'Textiles',
          'Chemical & Materials',
          'Medical Devices',
          'Consumer Goods',
          'Agriculture',
        ]),
      300,
    ),
  );

export const fetchComplianceOptions = async (): Promise<DropdownOption[]> =>
  new Promise((resolve) =>
    setTimeout(
      () =>
        resolve([
          { id: 1, name: 'CE' },
          { id: 2, name: 'RoHS' },
          { id: 3, name: 'BIS' },
          { id: 4, name: 'FDA' },
          { id: 5, name: 'REACH' },
          { id: 6, name: 'None' },
        ]),
      300,
    ),
  );

export const fetchShippingTerms = async (): Promise<DropdownOption[]> =>
  new Promise((resolve) =>
    setTimeout(
      () =>
        resolve([
          { id: 1, name: 'EXW' },
          { id: 2, name: 'FOB' },
          { id: 3, name: 'CIF' },
          { id: 4, name: 'DDP' },
          { id: 5, name: 'Open' },
        ]),
      300,
    ),
  );

export const fetchLeadTimeOptions = async (): Promise<DropdownOption[]> =>
  new Promise((resolve) =>
    setTimeout(
      () =>
        resolve([
          { id: 1, name: '<7 days' },
          { id: 2, name: '1-4 wks' },
          { id: 3, name: '>4 wks' },
          { id: 4, name: 'Prototype' },
        ]),
      300,
    ),
  );

export const useSourcingIntent = () => {
  const router = useRouter();

  const {
    data: countries = [],
    isLoading: loadingCountries,
    error: countriesError,
  } = useCountries();

  const { mutateAsync: createSourcingIntent } = useCreateSourcingIntent({
    onSuccess: handleSuccess,
    onError: handleError,
  });

  function handleSuccess() {
    toast.success('Sourcing Intent created successfully!');
    router.push('/buyer/invite-team');
  }

  function handleError(error: AxiosError<{ message: string }>) {
    toast.error(
      error.response?.data?.message ||
        'Sourcing intent creation failed! Please check and fill all the required data.',
    );
  }

  const handleSubmit = async () => {
    const transformedData = transformFormData(formData);
    await createSourcingIntent(transformedData);
  };

  // "No preference" option for sourcing_from
  const NO_PREFERENCE_OPTION: DropdownOption = { id: -1, name: 'No preference' };

  const [formData, setFormData] = useState<FormDataType>({
    sourcing_for: [],
    sourcing_from: [],
    use_case: [],
    product_categories: [],
    compliance_reqs: [],
    pref_shipping_term: NO_PREFERENCE_OPTION,
    target_lead_time: [],
  });

  const [useCases, setUseCases] = useState<DropdownOption[]>([]);
  const [productCategories, setProductCategories] = useState<string[]>([]);
  const [complianceOptions, setComplianceOptions] = useState<DropdownOption[]>([]);
  const [shippingTerms, setShippingTerms] = useState<DropdownOption[]>([]);
  const [leadTimeOptions, setLeadTimeOptions] = useState<DropdownOption[]>([]);

  // Load all static data
  useEffect(() => {
    const loadStatic = async () => {
      const [useCasesRes, categoriesRes, complianceRes, shippingRes, leadTimesRes] =
        await Promise.all([
          fetchUseCases(),
          fetchProductCategories(),
          fetchComplianceOptions(),
          fetchShippingTerms(),
          fetchLeadTimeOptions(),
        ]);

      setUseCases(useCasesRes);
      setProductCategories(categoriesRes);
      setComplianceOptions(complianceRes);
      setShippingTerms(shippingRes);
      setLeadTimeOptions(leadTimesRes);

      // Set defaults
      setFormData((prev) => ({
        ...prev,
        sourcing_from: [NO_PREFERENCE_OPTION],
      }));
    };
    loadStatic();
  }, []);

  // Handle dropdown selections (sourcing_for, sourcing_from)
  const handleDropdownSelect = (
    field: 'sourcing_for' | 'sourcing_from',
    option: DropdownOption,
  ) => {
    setFormData((prev) => {
      if (field === 'sourcing_from') {
        if (option.id === NO_PREFERENCE_OPTION.id) {
          // If "No preference" is selected, clear all others
          return {
            ...prev,
            [field]: [NO_PREFERENCE_OPTION],
          };
        } else {
          // Remove "No preference" if selecting specific countries
          const currentValues = prev[field].filter((item) => item.id !== NO_PREFERENCE_OPTION.id);
          const isSelected = currentValues.some((item) => item.id === option.id);
          const newValues = isSelected
            ? currentValues.filter((item) => item.id !== option.id)
            : [...currentValues, option];
          return {
            ...prev,
            [field]: newValues,
          };
        }
      }

      // Default behavior for sourcing_for
      const isSelected = prev[field].some((item) => item.id === option.id);
      return {
        ...prev,
        [field]: isSelected
          ? prev[field].filter((item) => item.id !== option.id)
          : [...prev[field], option],
      };
    });
  };

  // Handle checkbox group selections (use_case, compliance_reqs, pref_shipping_term)
  const handleCheckboxChange = (
    field: 'use_case' | 'compliance_reqs',
    option: DropdownOption,
    checked: boolean,
  ) => {
    setFormData((prev) => {
      const currentValues = prev[field];
      const isSelected = currentValues.some((item) => item.id === option.id);

      return {
        ...prev,
        [field]: checked
          ? isSelected
            ? currentValues // already present, no need to add
            : [...currentValues, option]
          : currentValues.filter((item) => item.id !== option.id),
      };
    });
  };

  // Handle product categories (string array)
  const handleProductCategoryChange = (category: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      product_categories: checked
        ? prev.product_categories.includes(category)
          ? prev.product_categories // already present
          : [...prev.product_categories, category]
        : prev.product_categories.filter((item) => item !== category),
    }));
  };

  // Handle single selection for shipping term
  const handleShippingTermChange = (optionId: number, checked: boolean) => {
    setFormData((prev) => {
      if (!checked) {
        // If unchecked, reset to "No preference"
        return {
          ...prev,
          pref_shipping_term: NO_PREFERENCE_OPTION,
        };
      }
      // Find the selected option from shippingTerms
      const selectedOption =
        shippingTerms.find((option) => option.id === optionId) || NO_PREFERENCE_OPTION;
      return {
        ...prev,
        pref_shipping_term: selectedOption,
      };
    });
  };

  const handleLeadTimeSelect = (value: string) => {
    const selected = leadTimeOptions.find((opt) => opt.name === value);
    if (!selected) return;

    setFormData((prev) => {
      const isAlreadySelected = prev.target_lead_time.some((item) => item.id === selected.id);
      return {
        ...prev,
        target_lead_time: isAlreadySelected
          ? prev.target_lead_time.filter((item) => item.id !== selected.id)
          : [...prev.target_lead_time, selected],
      };
    });
  };

  const transformFormData = (formData: FormDataType): SourcingIntentPayload => ({
    sourcing_for: formData?.sourcing_for?.map((item: DropdownOption) => item.id) ?? [],
    sourcing_from:
      formData?.sourcing_from?.[0]?.id === NO_PREFERENCE_OPTION.id
        ? []
        : (formData?.sourcing_from?.map((item: DropdownOption) => item.id) ?? []),

    use_cases: formData?.use_case?.map((item: DropdownOption) => item.id) ?? [],
    compliance_reqs: formData?.compliance_reqs?.map((item: DropdownOption) => item.id) ?? [],
    pref_shipping_term: formData?.pref_shipping_term?.id ?? 0,
    target_lead_time: formData?.target_lead_time?.[0]?.id ?? 0,
    product_categories: Array.isArray(formData?.product_categories)
      ? formData.product_categories.join(', ')
      : '',
  });

  return {
    formData,
    countries,
    useCases,
    productCategories,
    complianceOptions,
    shippingTerms,
    leadTimeOptions,
    loadingCountries,
    countriesError,
    handleDropdownSelect,
    handleCheckboxChange,
    handleProductCategoryChange,
    handleSubmit,
    setFormData,
    handleShippingTermChange,
    handleLeadTimeSelect,
    NO_PREFERENCE_OPTION,
    fetchUseCases,
    fetchProductCategories,
    fetchComplianceOptions,
    fetchShippingTerms,
    fetchLeadTimeOptions,
  };
};
