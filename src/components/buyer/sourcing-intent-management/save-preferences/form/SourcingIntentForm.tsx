'use client';
import {
  But<PERSON>,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox,
} from '@kratex-tradetech/kratex-ui-library';
import { FormDataType } from '../types';
import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';
import MultiSelectDropdown from '@/components/common/MultiSelectDropdown/MultiSelectDropdown';
import CheckboxGroup from '@/components/common/CheckboxGroup/CheckboxGroup';

interface Props {
  formData: FormDataType;
  countries: DropdownOption[];
  useCases: DropdownOption[];
  productCategories: string[];
  complianceOptions: DropdownOption[];
  shippingTerms: DropdownOption[];
  leadTimeOptions: DropdownOption[];
  loadingCountries: boolean;
  NO_PREFERENCE_OPTION: DropdownOption;
  handleDropdownSelect: (field: 'sourcing_for' | 'sourcing_from', option: DropdownOption) => void;
  handleCheckboxChange: (
    field: 'use_case' | 'compliance_reqs',
    option: DropdownOption,
    checked: boolean,
  ) => void;
  handleProductCategoryChange: (category: string, checked: boolean) => void;
  handleShippingTermChange: (optionId: number, checked: boolean) => void;
  handleLeadTimeSelect: (value: string) => void;
  onSubmit: () => void;
}

const SourcingIntentForm: React.FC<Props> = ({
  formData,
  countries,
  useCases,
  productCategories,
  complianceOptions,
  shippingTerms,
  leadTimeOptions,
  loadingCountries,
  NO_PREFERENCE_OPTION,
  handleDropdownSelect,
  handleCheckboxChange,
  handleProductCategoryChange,
  handleShippingTermChange,
  handleLeadTimeSelect,
  onSubmit,
}) => {
  const countryOptions = countries?.map((country) => ({
    id: country.id,
    name: country.name,
  }));

  const sourcingFromOptions = [NO_PREFERENCE_OPTION, ...countryOptions];

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}
      className="space-y-6"
    >
      {/* Sourcing For */}
      <div className="space-y-2">
        <Label htmlFor="sourcing-for">Sourcing For</Label>
        <MultiSelectDropdown
          id="sourcing-for"
          placeholder="Select countries to source for"
          options={countryOptions}
          selectedValues={formData.sourcing_for}
          onSelectionChange={(value) => handleDropdownSelect('sourcing_for', value)}
          isDataLoading={loadingCountries}
        />
      </div>

      {/* Sourcing From */}
      <div className="space-y-2">
        <Label htmlFor="sourcing-from">Sourcing From</Label>
        <MultiSelectDropdown
          id="sourcing-from"
          placeholder="Select countries to source from"
          options={sourcingFromOptions}
          selectedValues={formData.sourcing_from}
          onSelectionChange={(value) => handleDropdownSelect('sourcing_from', value)}
          isDataLoading={loadingCountries}
        />
      </div>

      {/* Use Case */}
      <CheckboxGroup
        label="Use Case"
        options={useCases.map((option) => option.name)}
        selectedValues={formData.use_case.map((option) => option.name)}
        onSelectionChange={(name) => {
          const option = useCases.find((opt) => opt.name === name);
          if (option) {
            const isSelected = formData.use_case.some((s) => s.id === option.id);
            handleCheckboxChange('use_case', option, !isSelected);
          }
        }}
        idPrefix="use-case"
      />

      {/* Product Categories */}
      <CheckboxGroup
        label="Product Categories"
        options={productCategories}
        selectedValues={formData.product_categories}
        onSelectionChange={(category) => {
          const isSelected = formData.product_categories.includes(category);
          handleProductCategoryChange(category, !isSelected);
        }}
        idPrefix="category"
      />

      {/* Compliance Requirements */}
      <CheckboxGroup
        label="Compliance Requirements"
        options={complianceOptions.map((option) => option.name)}
        selectedValues={formData.compliance_reqs.map((option) => option.name)}
        onSelectionChange={(name) => {
          const option = complianceOptions.find((opt) => opt.name === name);
          if (option) {
            const isSelected = formData.compliance_reqs.some((s) => s.id === option.id);
            handleCheckboxChange('compliance_reqs', option, !isSelected);
          }
        }}
        idPrefix="compliance"
        gridCols="grid-cols-3"
      />

      {/* Shipping Term */}
      <div className="space-y-2">
        <Label>Preferred Shipping Term</Label>
        <div className="grid grid-cols-3 gap-2">
          {shippingTerms.map((option) => (
            <div key={option.id} className="flex items-center space-x-2">
              <Checkbox
                id={`shipping-term-${option.id}`}
                checked={formData.pref_shipping_term?.id === option.id}
                onCheckedChange={(checked) => handleShippingTermChange(option.id, checked === true)}
              />
              <Label htmlFor={`shipping-term-${option.id}`}>{option.name}</Label>
            </div>
          ))}
        </div>
      </div>

      {/* Lead Time */}
      <div className="space-y-2">
        <Label htmlFor="lead-time">Target Lead Time</Label>
        <Select
          value={formData?.target_lead_time?.[0]?.name ?? ''}
          onValueChange={handleLeadTimeSelect}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select lead time" />
          </SelectTrigger>
          <SelectContent>
            {leadTimeOptions.map((time) => (
              <SelectItem key={time.id} value={time.name}>
                {time.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Submit */}
      <div className="pt-4">
        <Button type="submit" className="w-full cursor-pointer">
          Save Sourcing Preferences
        </Button>
      </div>
    </form>
  );
};

export default SourcingIntentForm;
