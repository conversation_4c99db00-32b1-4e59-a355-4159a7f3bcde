'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kratex-tradetech/kratex-ui-library';

import { useSourcingIntent } from '@/components/buyer/sourcing-intent-management/save-preferences/useSourcingIntent';
import SourcingIntentForm from '@/components/buyer/sourcing-intent-management/save-preferences/form/SourcingIntentForm';

const SourcingIntent = () => {
  const {
    formData,
    countries,
    useCases,
    productCategories,
    complianceOptions,
    shippingTerms,
    leadTimeOptions,
    loadingCountries,
    NO_PREFERENCE_OPTION,
    handleDropdownSelect,
    handleCheckboxChange,
    handleProductCategoryChange,
    handleShippingTermChange,
    handleLeadTimeSelect,
    handleSubmit,
  } = useSourcingIntent();

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Define Sourcing Intent</CardTitle>
            <CardDescription>
              Provide information about your sourcing preferences and requirements to personalise
              your catalogue.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SourcingIntentForm
              formData={formData}
              countries={countries}
              useCases={useCases}
              productCategories={productCategories}
              complianceOptions={complianceOptions}
              shippingTerms={shippingTerms}
              leadTimeOptions={leadTimeOptions}
              loadingCountries={loadingCountries}
              NO_PREFERENCE_OPTION={NO_PREFERENCE_OPTION}
              handleDropdownSelect={handleDropdownSelect}
              handleCheckboxChange={handleCheckboxChange}
              handleProductCategoryChange={handleProductCategoryChange}
              handleShippingTermChange={handleShippingTermChange}
              handleLeadTimeSelect={handleLeadTimeSelect}
              onSubmit={handleSubmit}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SourcingIntent;
