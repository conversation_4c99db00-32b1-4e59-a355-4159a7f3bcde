'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { Globe } from 'lucide-react';

type InfoType = {
  name: string;
  id: number;
};

interface jurisdictionData {
  state: InfoType;
  country: InfoType;
}

interface JurisdictionCardProps {
  data: jurisdictionData;
}

const JurisdictionCard: React.FC<JurisdictionCardProps> = ({ data }) => {
  return (
    <Card className="!gap-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Globe className="h-4 w-4" />
            Jurisdiction Information
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label className="text-gray-600">Country</Label>
            <p className="text-sm">{data.country?.name}</p>
          </div>
          <div className="space-y-2">
            <Label className="text-gray-600">State</Label>
            <p className="text-sm">{data.state?.name}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JurisdictionCard;
