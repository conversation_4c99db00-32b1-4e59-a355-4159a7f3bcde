'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { User } from 'lucide-react';

interface PersonalInfoData {
  full_name: string;
  email: string;
  phone_number: string;
}

interface PersonalInfoProps {
  data: PersonalInfoData;
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({ data }) => {
  return (
    <Card className="!gap-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <User className="h-4 w-4" />
            Account Basics
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label className="text-gray-600">Full Name</Label>
            <p className="text-sm">{data.full_name}</p>
          </div>
          <div className="space-y-2">
            <Label className="text-gray-600">Email</Label>
            <p className="text-sm">{data.email}</p>
          </div>
          <div className="space-y-2">
            <Label className="text-gray-600">Phone Number</Label>
            <p className="text-sm">{data.phone_number}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalInfo;
