import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { Building } from 'lucide-react';

interface OrganizationInfoData {
  org_type: { name: string; id: number };
  org_name: string;
  registration_number: string;
  tax_id: string;
  business_address: string;
}

interface OrganizationInfoProps {
  data: OrganizationInfoData;
}

const OrganizationInfo: React.FC<OrganizationInfoProps> = ({ data }) => {
  return (
    <Card className="!gap-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Building className="h-4 w-4" />
            Organization Information
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label className="text-gray-600">Organization Type</Label>
            <p className="text-sm">{data.org_type?.name}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrganizationInfo;
