'use client';

import { FC, ReactNode } from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, Globe } from 'lucide-react';
import JurisdictionCard from '../cards/JurisdictionCard';
import OrganizationInfo from '../cards/OrganisationInfo';
import PersonalInfo from '../cards/PersonalInfoCard';

interface BasicInformationDisplayProps {
  data: any;
  onEdit?: () => void;
  showEditButton?: boolean;
  title?: string;
  icon?: ReactNode;
  className?: string;
}

const BasicInformationDisplay: FC<BasicInformationDisplayProps> = ({
  data,
  onEdit,
  showEditButton = true,
  title = 'Basic Information',
  icon = <Globe className="h-4 w-4" />,
  className = '',
}) => {
  return (
    <Card className={`${className} !gap-4`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          </CardTitle>
          {showEditButton && onEdit && (
            <Button className="cursor-pointer" variant="outline" size="sm" onClick={onEdit}>
              <Edit className="mr-1 h-3 w-3" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <PersonalInfo data={data.accountbasics_info} />
        <OrganizationInfo data={data.organization_info} />
        <JurisdictionCard data={data.jurisdiction} />
      </CardContent>
    </Card>
  );
};

export default BasicInformationDisplay;
