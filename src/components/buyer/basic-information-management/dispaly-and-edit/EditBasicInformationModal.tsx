import { FC } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { Loader2Icon, Timer } from 'lucide-react';

interface Props {
  buyerInfo: any;
  name: string;
  isOpen: boolean;
  isSaving: boolean;
  isPending: boolean;
  updateName: (value: string) => void;
  onClose: () => void;
  onSave: () => void;
}

const EditBasicInformationModal: FC<Props> = ({
  buyerInfo,
  name,
  isOpen,
  isSaving,
  isPending = false,
  updateName,
  onClose,
  onSave,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-4xl min-w-3xl overflow-auto">
        <DialogHeader>
          <DialogTitle>Edit Basic Information</DialogTitle>
          {isPending && (
            <div className="mt-2 flex items-start gap-1 rounded-md border border-yellow-300 bg-yellow-50 p-4 text-sm text-yellow-800">
              <Timer size={'16px'} />
              <p>
                <strong className="font-medium">Update Pending:</strong> A previous update request
                is still under review. You’ll be able to make changes once it’s processed.
              </p>
            </div>
          )}
        </DialogHeader>
        <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="displayName">
              Name <span className="text-red-500">*</span>
            </Label>
            <Input id="displayName" value={name} onChange={(e) => updateName(e.target.value)} />
          </div>
          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              value={buyerInfo?.accountbasics_info?.email}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="phone">
              Phone
            </Label>
            <Input
              id="phone"
              value={buyerInfo?.accountbasics_info.phone_number}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="orgType">
              Organisation Type
            </Label>
            <Input
              id="orgType"
              value={buyerInfo?.organization_info?.org_type?.name}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="orgType">
              Organisation Name
            </Label>
            <Input
              id="orgType"
              value={buyerInfo?.organization_info?.org_name}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="state">
              State
            </Label>
            <Input
              id="state"
              value={buyerInfo?.jurisdiction?.state?.name}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-gray-600" htmlFor="country">
              Country
            </Label>
            <Input
              id="country"
              value={buyerInfo?.jurisdiction?.country?.name}
              disabled
              className="bg-gray-50"
            />
          </div>
        </div>

        <Button
          onClick={onSave}
          disabled={isSaving || isPending}
          className="mx-auto mt-4 w-fit cursor-pointer"
        >
          {isSaving && <Loader2Icon className="animate-spin" />}
          {isSaving ? 'Saving...' : 'Update Details'}
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default EditBasicInformationModal;
