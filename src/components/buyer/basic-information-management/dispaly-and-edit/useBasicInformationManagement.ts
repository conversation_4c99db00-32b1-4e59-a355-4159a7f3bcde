import { AxiosError } from 'axios';
import { toast } from 'sonner';
import { useUpdateBuyerInfo } from '@/services/buyer/useUpdateBuyerInformation';
import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { useEffect, useState } from 'react';
import { FinalizeInfoResponse } from '@/types/finalizeResponse.types';
import { useQueryClient } from '@tanstack/react-query';

function mapApiToOnboardingData(apiData: FinalizeInfoResponse) {
  return {
    accountbasics_info: {
      full_name: apiData.buyer_details.display_name || '',
      email: apiData.email || '',
      phone_number: apiData.phone || '',
    },
    jurisdiction: {
      country: {
        name: apiData.jurisdiction?.country?.name || '',
        id: apiData.jurisdiction?.country?.id || 0,
      },
      state: {
        name: apiData.jurisdiction?.state?.name || '',
        id: apiData.jurisdiction?.state?.id || 0,
      },
    },
    organization_info: {
      org_type: {
        name: apiData.organisation_type.name || '',
        id: apiData.organisation_type.id || 0,
      },
      org_name: apiData.buyer_details.display_name,
      registration_number: '',
      tax_id: '',
      business_address: '',
    },
    buyer_details: apiData.buyer_details || {},
    pending_request: apiData?.pending_request || false,
  };
}

const useBasicInformationManagement = () => {
  const queryClient = useQueryClient();
  const [displayName, setDisplayName] = useState('');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data: buyerData, isLoading: isBuyerLoading } = useGetUserInfo('buyer');

  const onboardingData = buyerData
    ? {
        ...mapApiToOnboardingData(buyerData),
      }
    : null;

  const handleError = (error: AxiosError) => {
    const message = error instanceof Error ? error.message : 'An unknown error occurred.';
    toast.error(message);
  };

  function handleUpdateSuccess() {
    toast.success('An update request sent to the admin!');
    closeModal();
    queryClient.invalidateQueries({
      queryKey: ['buyer', 'info'],
    });
  }

  const { mutateAsync: saveUpdateBuyerInfo, isPending: isUpdating } = useUpdateBuyerInfo({
    onSuccess: handleUpdateSuccess,
    onError: handleError,
  });

  const handleUpdateClick = async () => {
    saveUpdateBuyerInfo({
      name: displayName,
      organisation_type: onboardingData?.organization_info?.org_type?.id || 0,
      jurisdiction: onboardingData?.jurisdiction?.state?.id || 0, // BE requires state id here
      buyer_details: onboardingData?.buyer_details || {},
    });
  };

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  useEffect(() => {
    if (buyerData) {
      setDisplayName(buyerData.buyer_details.display_name || '');
    }
  }, [buyerData]);

  return {
    isPending: isBuyerLoading,
    isUpdating,
    buyerInfo: onboardingData,
    isModalOpen,
    displayName,
    setDisplayName,
    openModal,
    closeModal,
    saveUpdateBuyerInfo: handleUpdateClick,
  };
};

export default useBasicInformationManagement;
