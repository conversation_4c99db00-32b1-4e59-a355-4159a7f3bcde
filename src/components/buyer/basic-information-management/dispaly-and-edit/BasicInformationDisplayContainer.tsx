'use client';

import { FC, ReactNode } from 'react';
import useBasicInformationManagement from './useBasicInformationManagement';
import BasicInformationDisplay from './BasicInformationDisplay';
import EditBasicInformationModal from './EditBasicInformationModal';

interface BasicInformationDisplayContainerProps {
  userType?: 'buyer' | 'seller';
  showEditButton?: boolean;
  title?: string;
  icon?: ReactNode;
  className?: string;
}

const BasicInformationDisplayContainer: FC<BasicInformationDisplayContainerProps> = ({
  showEditButton = true,
  title,
  icon,
  className,
}) => {
  const {
    isUpdating,
    isPending,
    buyerInfo,
    isModalOpen,
    displayName,
    setDisplayName,
    openModal,
    closeModal,
    saveUpdateBuyerInfo,
  } = useBasicInformationManagement();

  if (isPending) {
    return (
      <div className="animate-pulse">
        <div className="h-48 rounded-lg bg-gray-200"></div>
      </div>
    );
  }

  if (!buyerInfo) {
    return (
      <div className="py-8 text-center">
        <p className="text-gray-500">No basic information available</p>
      </div>
    );
  }

  return (
    <>
      <BasicInformationDisplay
        data={buyerInfo}
        onEdit={showEditButton ? openModal : undefined}
        showEditButton={showEditButton}
        title={title}
        icon={icon}
        className={className}
      />

      {showEditButton && (
        <EditBasicInformationModal
          buyerInfo={buyerInfo}
          isPending={buyerInfo?.pending_request}
          name={displayName}
          isOpen={isModalOpen}
          isSaving={isUpdating}
          onSave={saveUpdateBuyerInfo}
          updateName={setDisplayName}
          onClose={closeModal}
        />
      )}
    </>
  );
};

export default BasicInformationDisplayContainer;
