import { Button } from '@kratex-tradetech/kratex-ui-library';
import { Badge, Building2, Plus } from 'lucide-react';

const EntityTypeAndKycTab = () => {
  return (
    <div className="rounded-lg border bg-white p-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Entity Type & KYC</h3>
          <p className="text-gray-600">{`Manage buyer's entity information and KYC documents`}</p>
        </div>
        <Button variant={'outline'} className="cursor-pointer">
          <Plus className="h-4 w-4" />
          Add Entity Details
        </Button>
      </div>

      <div className="rounded-lg border p-4">
        <div className="mb-4 flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded bg-blue-100">
            <Building2 className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h4 className="font-medium">Corporation</h4>
            <p className="text-sm text-gray-600">Delaware Branch</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 text-sm">
          <div>
            <p className="text-gray-600">Entity Type</p>
            <p className="font-medium">C-Corporation</p>
          </div>
          <div>
            <p className="text-gray-600">Registration Number</p>
            <p className="font-medium">DE-*********</p>
          </div>
          <div>
            <p className="text-gray-600">Tax ID</p>
            <p className="font-medium">****-***-1234</p>
          </div>
          <div>
            <p className="text-gray-600">KYC Status</p>
            <Badge className="bg-green-100 text-green-800">Verified</Badge>
          </div>
        </div>

        <div className="mt-6 rounded bg-gray-50 p-4">
          <p className="text-sm text-gray-600">
            <strong>Note:</strong> Entity information is verified and securely stored. Only
            authorized personnel can view the complete details.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EntityTypeAndKycTab;
