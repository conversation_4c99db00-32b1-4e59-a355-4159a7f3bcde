import { getTokenFromCookie } from '@/lib/cookies';

export function patchGlobalFetchWithAuth() {
  const originalFetch = window.fetch;

  window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    try {
      const token = getTokenFromCookie();

      const authHeaders: Record<string, string> = token ? { Authorization: `Bearer ${token}` } : {};

      const mergedHeaders: HeadersInit = {
        ...(init?.headers ?? {}),
        ...authHeaders,
      };

      const modifiedInit: RequestInit = {
        ...init,
        headers: mergedHeaders,
      };

      return await originalFetch(input, modifiedInit);
    } catch (err) {
      console.error('Fetch wrapper error', err);
      throw err;
    }
  };
}
