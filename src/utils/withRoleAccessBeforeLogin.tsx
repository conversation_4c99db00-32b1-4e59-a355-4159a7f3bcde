'use client';

export enum UserTypeLabel {
  ADMIN = 1,
  BUYER = 2,
  SUPPLIER = 3,
}

// Loading Components
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DashboardRedirectLoader = () => (
  <div className="flex min-h-screen items-center justify-center">
    <div className="text-center">
      <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-black" />
      <p>Redirecting to Dashboard</p>
    </div>
  </div>
);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const AccessDeniedLoader = ({ role }: { role: 'buyer' | 'supplier' }) => (
  <div className="flex min-h-screen items-center justify-center">
    <div className="text-center">
      <h2 className="mb-2 text-2xl font-bold">Access Denied</h2>
      <p className="text-muted-foreground mb-4">You don&#39;t have access to {role} flow</p>
      <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-black" />
    </div>
  </div>
);

// Main HOC
// export function withRoleAccessBeforeLogin<P extends object>(
//   WrappedComponent: React.ComponentType<P>,
//   requiredRole: UserTypeEnum.BUYER | UserTypeEnum.SUPPLIER,
// ) {
//   return function WithRoleAccessBeforeLoginComponent(props: P) {
//     return <WrappedComponent {...props} />;
//   };
// }

// // Convenience HOCs for specific roles
// export function withBuyerAccessBeforeLogin<P extends object>(
//   WrappedComponent: React.ComponentType<P>,
// ) {
//   return withRoleAccessBeforeLogin(WrappedComponent, UserTypeEnum.BUYER);
// }

// export function withSupplierAccessBeforeLogin<P extends object>(
//   WrappedComponent: React.ComponentType<P>,
// ) {
//   return withRoleAccessBeforeLogin(WrappedComponent, UserTypeEnum.SUPPLIER);
// }
