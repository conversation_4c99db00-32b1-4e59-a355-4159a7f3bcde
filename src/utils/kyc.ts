import { KYCStatusData } from '@/constants/kyc-status';

export const getKycStatus = (statusValue: number) => {
  const entry = Object.values(KYCStatusData).find((item) => item.value === statusValue);
  return entry?.label ?? 'Unknown';
};

// TODO: this should be later converted into a genrator function with a consistent structure
// after confirmation, like : `kyc${userTypeLabel}${orgType}${countryName}`
export const kycFormKeyMap: Record<number, string> = {
  1: 'kycBuyerIndividualIndia',
  2: 'kycBuyerSoleProprietorIndia',
  3: 'kycBuyerRegisteredCompanyIndia',
  4: 'kycBuyerGovernmentBodyIndia',
  5: 'kycBuyerNonProfitIndia',
  6: 'kycBuyerBuyingAgentIndia',
};
