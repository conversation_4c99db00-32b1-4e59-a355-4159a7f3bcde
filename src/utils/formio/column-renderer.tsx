'use client';

import type { UseFormReturn } from 'react-hook-form';
import type { FormioColumn } from './types';
import { FormioComponentRenderer } from './componentRenderer';

interface ColumnRendererProps {
  column: FormioColumn;
  form: UseFormReturn<any>;
}

export const ColumnRenderer: React.FC<ColumnRendererProps> = ({ column, form }) => {
  // Calculate responsive width classes based on Form.io width (1-12 scale)
  const getWidthClass = (width: number) => {
    const percentage = Math.round((width / 12) * 100);
    // Map to Tailwind classes
    if (percentage <= 25) return 'w-full md:w-1/4';
    if (percentage <= 33) return 'w-full md:w-1/3';
    if (percentage <= 50) return 'w-full md:w-1/2';
    if (percentage <= 66) return 'w-full md:w-2/3';
    if (percentage <= 75) return 'w-full md:w-3/4';
    return 'w-full';
  };

  return (
    <div className={`${getWidthClass(column.width)} px-2`}>
      {column.components?.map((component, index) => (
        <div key={component.key || index} className="mb-4">
          <FormioComponentRenderer component={component} form={form} />
        </div>
      ))}
    </div>
  );
};
