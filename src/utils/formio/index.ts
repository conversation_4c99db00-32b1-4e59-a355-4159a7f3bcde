/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { FormioColumnsComponent, FormioComponent } from './types';

// Types
export type { FormioComponent, FormioColumn, FormioColumnsComponent, FormioSchema } from './types';

// Core Functions
export { createZodSchema, getDefaultValues } from './schema-generator';

// Components
export { FormioComponentRenderer } from './componentRenderer';
export { ColumnRenderer } from './column-renderer';
export { FormioFormRenderer } from './form-renderer';

// Utility functions
export const isColumnsComponent = (component: any): component is FormioColumnsComponent => {
  return component.type === 'columns';
};

export const isFormioComponent = (component: any): component is FormioComponent => {
  return component.key !== undefined || component.input !== undefined;
};
