import { KYCStatusData } from '@/constants/kyc-status';
import { FileId } from '@/store/fileIds.store';
import { KYCPayload } from '@/types/kyc.types';

// Function used for injecting the file upload respones to the payload

// incase of sandbox related verification :
// status wil be either 0 or 1
// incase of non-sandbox related verification:
// status will be 2(under review)

export const kycPayloadGenerator = (
  fileIds: FileId | null, // file upload may not be required
  keyword: string,
  data: Record<string, string>,
  status: number,
) => {
  const updatedData = { ...data };
  if (fileIds) {
    Object.entries(fileIds).forEach(([key, value]) => {
      if (key in updatedData) {
        updatedData[key] = value;
      }
    });
  }
  const payload: KYCPayload = {
    keyword,
    status: status === 0 ? KYCStatusData.ACTIVE.value : KYCStatusData.UNDER_REVIEW.value,
    submission_data: updatedData,
  };
  return payload;
};
