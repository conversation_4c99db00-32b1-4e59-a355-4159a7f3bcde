/* eslint-disable @typescript-eslint/no-explicit-any */

export interface FormioFileConfig {
  name: string;
  url: string;
  options?: {
    withCredentials?: boolean;
    [key: string]: any;
  };
}

export interface FormioFileType {
  label: string;
  value: string;
}

export interface FormioComponent {
  disabled?: boolean | undefined;
  key?: string;
  type: string;
  input?: boolean;
  label?: string;
  placeholder?: string;
  description?: string;
  defaultValue?: any;
  customClass?: string;
  values?: Array<{ label: string; value: string }>;
  showValidations?: boolean;
  applyMaskOn?: string;
  validateWhenHidden?: boolean;
  tableView?: boolean;
  // a = alphabetical
  // 9 = numeric
  // * = alphanumeric
  inputMask?: 'a' | '9' | '*' | undefined | string;
  disableOnInvalid?: boolean;
  data?: {
    values?: Array<{ label: string; value: string }>;
  };
  dataSrc?: string;
  searchEnabled?: boolean;
  properties?: Record<string, any>;
  ext?: Record<string, any>;
  validate?: {
    required?: boolean;
    requiredMessage?: string;
    pattern?: string;
    patternMessage?: string;
    custom?: string;
    minLength?: number | string;
    maxLength?: number | string;
    min?: number;
    max?: number;
    onlyAvailableItems?: boolean;
  };
  columns?: Array<FormioComponent>;
  components?: FormioComponent[];
  file?: FormioFileConfig;
  fileTypes?: FormioFileType[];
  rows?: number;
  format?: string;
  enableDate?: boolean;
  enableTime?: boolean;
  /**
   * Button specific properties (e.g., for `button` type components)
   */
  action?: string;
  event?: string;
  custom?: string;
  /**
   * Visual properties used by some component libraries (e.g., bootstrap theme buttons)
   */
  theme?: string;
  size?: string;
  /**
   * Raw HTML content for 'htmlelement' components.
   */
  content?: string;
  /**
   * Conditional display logic for components.
   */
  conditional?: Record<string, any>;
}

export interface FormioColumn {
  pull: number;
  push: number;
  size: string;
  width: number;
  offset: number;
  components: FormioComponent[];
}

export interface FormioColumnsComponent {
  type: 'columns';
  columns: FormioColumn[];
  properties?: Record<string, any>;
}

export interface FormioFormResponse {
  id?: number;
  schema: FormioSchema;
  keyword: string;
}

export interface FormioSchema {
  type: 'form';
  title: string;
  components: Array<FormioComponent | FormioColumnsComponent>;
}
