import Brandproof from '@/components/reusable/brandproof/Brandproof';
import CustomVerificationDropdown from '@/components/reusable/CustomVerificationDropdown';
import GSTNVerification from '@/components/reusable/GSTNVerification';
import PhoneField from '@/components/reusable/PhoneField';
import { UseFormReturn } from 'react-hook-form';

export const renderCustomModule = (
  key: string | undefined,
  form: UseFormReturn,
  isDisabled?: boolean,
) => {
  switch (key) {
    case 'gstnVerifcationModule':
      return <GSTNVerification form={form} />; //input field for GSTN verification

    case 'verificationDropdown': //dropdown component for Aadhaar, PAN, or future new ways of verification etc.
      return <CustomVerificationDropdown name={key} form={form} />;

    case 'phone':
      return <PhoneField isDisabled={isDisabled} form={form} name={key} />;
    case 'brandproofModule':
      return <Brandproof form={form} />;
    default:
      return <></>;
  }
};
