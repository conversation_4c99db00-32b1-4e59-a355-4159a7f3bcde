'use client';
/* eslint-disable @typescript-eslint/no-explicit-any */

import { Card, CardContent, Form } from '@kratex-tradetech/kratex-ui-library';
import { UseFormReturn } from 'react-hook-form';
import { ColumnRenderer } from './column-renderer';
import { FormioComponentRenderer } from './componentRenderer';
import type { FormioColumnsComponent, FormioComponent, FormioSchema } from './types';

interface FormioFormRendererProps {
  form: UseFormReturn<any>;
  formioJson: FormioSchema;
  onSubmit: (data: any) => void;
  children?: React.ReactNode;
  className?: string;
  showCard?: boolean;
  formSubmitSuccessCallback?: () => void;
  formSubmitErrorCallback?: () => void;
}

const renderComponents = (
  components: (FormioComponent | FormioColumnsComponent)[],
  form: UseFormReturn<any>,
  formSubmitSuccessCallback?: () => void,
  formSubmitErrorCallback?: () => void,
) => {
  return components.map((component, index) => {
    if (component.type === 'columns') {
      const columnsComponent = component as FormioColumnsComponent;
      return (
        <div key={index} className="-mx-2 mb-6 flex flex-wrap">
          {columnsComponent.columns.map((column, colIndex) => (
            <ColumnRenderer key={colIndex} column={column} form={form} />
          ))}
        </div>
      );
    } else {
      const singleComponent = component as FormioComponent;
      return (
        <div key={singleComponent.key || index} className="mb-4">
          <FormioComponentRenderer
            component={singleComponent}
            form={form}
            formSubmitSuccessCallback={formSubmitSuccessCallback}
            formSubmitErrorCallback={formSubmitErrorCallback}
          />
        </div>
      );
    }
  });
};

const FormContent = ({
  children,
  formioJson,
  form,
  onSubmit,
  formSubmitSuccessCallback,
  formSubmitErrorCallback,
}: FormioFormRendererProps & {
  form: UseFormReturn<any>;
}) => {
  const FormFields = renderComponents(
    formioJson?.components,
    form,
    formSubmitSuccessCallback,
    formSubmitErrorCallback,
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-6">
          {FormFields}
          {children}
        </div>
      </form>
    </Form>
  );
};

export const FormioFormRenderer: React.FC<FormioFormRendererProps> = ({
  form,
  formioJson,
  className = '',
  children,
  showCard = true,
  onSubmit,
  formSubmitSuccessCallback,
  formSubmitErrorCallback,
}) => {
  // const schema = useMemo(() => createZodSchema(formioJson.components), [formioJson.components]);
  // const form = useForm({
  //   resolver: zodResolver(schema),
  //   defaultValues: getDefaultValues(formioJson.components),
  // });

  if (showCard) {
    return (
      <Card className={`mx-auto w-full max-w-4xl ${className}`}>
        <CardContent className="p-6">
          <FormContent
            formioJson={formioJson}
            form={form}
            onSubmit={onSubmit}
            formSubmitSuccessCallback={formSubmitSuccessCallback}
            formSubmitErrorCallback={formSubmitErrorCallback}
          >
            {children}
          </FormContent>
        </CardContent>
      </Card>
    );
  } else {
    return (
      <FormContent
        onSubmit={onSubmit}
        formioJson={formioJson}
        form={form}
        formSubmitSuccessCallback={formSubmitSuccessCallback}
        formSubmitErrorCallback={formSubmitErrorCallback}
      >
        {children}
      </FormContent>
    );
  }
};
