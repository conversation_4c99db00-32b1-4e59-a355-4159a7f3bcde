// utils/countryTransform.ts
import { Country } from '@/types/newSupplier.types';

export const transformUICountryToInternal = (uiCountry: any): Country | null => {
  if (!uiCountry?.alpha2) return null;

  return {
    id: uiCountry.id ?? uiCountry.alpha2, // fallback if id is not provided
    iso2: uiCountry.alpha2,
    name: uiCountry.name,
    iso3: uiCountry.alpha3,
    alpha2: uiCountry.alpha2,
    alpha3: uiCountry.alpha3,
    emoji: uiCountry.emoji ?? '',
    countryCallingCodes: uiCountry.countryCallingCodes ?? [],
    ioc: uiCountry.ioc ?? '',
  };
};
