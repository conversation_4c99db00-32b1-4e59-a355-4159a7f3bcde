import { create } from 'zustand';

type CustomData = Record<string, unknown>;

interface CustomDataState {
  sandboxResponse: CustomData | null;
  moduleData: CustomData | null;
  setSandboxResponse: (sandboxResponse: CustomData) => void;
  setModuleData: (sandboxResponse: CustomData) => void;
  clearAllModuleData: () => void;
  clearAllSandboxResponse: () => void;
}

export const useCustomDataStore = create<CustomDataState>((set) => ({
  sandboxResponse: null,
  moduleData: null,
  setSandboxResponse: (sandboxResponse) =>
    set((state) => ({
      sandboxResponse: { ...state.sandboxResponse, ...sandboxResponse },
    })),
  setModuleData: (moduleData) =>
    set((state) => ({
      moduleData: { ...state.moduleData, ...moduleData },
    })),
  clearAllModuleData: () => set(() => ({ moduleData: null })),
  clearAllSandboxResponse: () => set(() => ({ sandboxResponse: null })),
}));
