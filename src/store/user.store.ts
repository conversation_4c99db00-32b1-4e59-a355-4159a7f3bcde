import { BuyerOnboardingData } from '@/types/newBuyer.types';
import { SupplierOnboardingData } from '@/types/newSupplier.types';
import { User } from '@/types/user.types';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const defaultUser: User | null = null;

type UserState = {
  userData: User | null;
  supplierOnboardingData: SupplierOnboardingData | null;
  buyerOnboardingData: BuyerOnboardingData | null;
  setUserData: (user: User) => void;
  clearUserData: () => void;
  updateUserData: (user: Partial<User> | null) => void;

  setSupplierOnboardingData: (data: SupplierOnboardingData) => void;
  clearSupplierOnboardingData: () => void;
  updateSupplierOnboardingData: (user: Partial<SupplierOnboardingData> | null) => void;

  setBuyerOnboardingData: (data: BuyerOnboardingData) => void;
  clearBuyerOnboardingData: () => void;
  updateBuyerOnboardingData: (user: Partial<BuyerOnboardingData> | null) => void;
};

export const userStore = create<UserState>()(
  persist(
    (set) => ({
      userData: defaultUser,
      setUserData: (user: User) => set({ userData: user }),
      clearUserData: () => set({ userData: defaultUser }),
      updateUserData: (updatedFields) =>
        set((state) => ({
          userData: state.userData
            ? { ...state.userData, ...updatedFields }
            : ({ ...updatedFields } as User),
        })),

      supplierOnboardingData: null,
      setSupplierOnboardingData: (data: SupplierOnboardingData) =>
        set({ supplierOnboardingData: data }),
      clearSupplierOnboardingData: () => set({ supplierOnboardingData: null }),
      updateSupplierOnboardingData: (updatedFields) =>
        set((state) => ({
          supplierOnboardingData: state.supplierOnboardingData
            ? { ...state.supplierOnboardingData, ...updatedFields }
            : ({ ...updatedFields } as SupplierOnboardingData), // required if starting from partial
        })),

      buyerOnboardingData: null,
      setBuyerOnboardingData: (data: BuyerOnboardingData) => set({ buyerOnboardingData: data }),
      clearBuyerOnboardingData: () => set({ buyerOnboardingData: null }),
      updateBuyerOnboardingData: (updatedFields) =>
        set((state) => ({
          buyerOnboardingData: state.buyerOnboardingData
            ? { ...state.buyerOnboardingData, ...updatedFields }
            : ({ ...updatedFields } as BuyerOnboardingData),
        })),
    }),
    {
      name: 'user-store',
    },
  ),
);
