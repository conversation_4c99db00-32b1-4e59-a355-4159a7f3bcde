import { create } from 'zustand';

interface AppConfig {
  API_BASE_URL: string;
  [key: string]: any;
}

interface ConfigState {
  config: AppConfig | null;
  isConfigLoaded: boolean;
  isConfigLoading: boolean;
  error: string | null;
  setConfig: (config: AppConfig) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

export const useConfigStore = create<ConfigState>((set) => ({
  config: null,
  isConfigLoaded: false,
  isConfigLoading: false,
  error: null,
  setConfig: (config) =>
    set({
      config,
      isConfigLoaded: true,
      isConfigLoading: false,
      error: null,
    }),
  setLoading: (loading) => set({ isConfigLoading: loading }),
  setError: (error) =>
    set({
      error,
      isConfigLoading: false,
      isConfigLoaded: false,
    }),
  reset: () =>
    set({
      config: null,
      isConfigLoaded: false,
      isConfigLoading: false,
      error: null,
    }),
}));
