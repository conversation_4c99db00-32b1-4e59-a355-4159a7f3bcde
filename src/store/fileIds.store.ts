import { create } from 'zustand';

export type FileId = { [key: string]: string };

interface FileIdsState {
  fileIds: FileId | null;
  setFileId: (fileIds: FileId) => void;
  clearFileIdsByKey: (fileIdsToClear: FileId) => void;
  clearFileIds: () => void;
}

export const useFileIdsStore = create<FileIdsState>((set) => ({
  fileIds: null,
  setFileId: (newFileIds) =>
    set((state) => ({
      fileIds: { ...state.fileIds, ...newFileIds },
    })),
  clearFileIdsByKey: (fileIdsToClear) =>
    set((state) => {
      if (!state.fileIds || !fileIdsToClear) return state;
      const updated = { ...state.fileIds };
      Object.keys(fileIdsToClear).forEach((key) => delete updated[key]);
      return { fileIds: Object.keys(updated).length ? updated : null };
    }),
  clearFileIds: () => set({ fileIds: null }),
}));
