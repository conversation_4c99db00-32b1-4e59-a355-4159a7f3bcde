'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';

import { ONBOARDING_STATES } from '@/constants/onboarding-status';
import { useGetMe } from '@/services/auth/useGetMe';
import { userStore } from '@/store/user.store';
import { UserTypeEnum, userTypeLabelMap } from '@/constants/user';

const BUYER_ROUTES: Record<string, string> = {
  [ONBOARDING_STATES.MARK_ACCOUNT_BASICS_FILLED]: '/buyer/kyc',
  [ONBOARDING_STATES.MARK_USER_KYC_SKIPPED]: '/buyer/sourcing-intent',
  [ONBOARDING_STATES.MARK_USER_KYC_FILLED]: '/buyer/sourcing-intent',
  [ONBOARDING_STATES.MARK_SOURCING_INTENT_FILLED]: '/buyer/invite-team',
  [ONBOARDING_STATES.MARK_MEMBER_INVITED]: '/buyer/finalize-onboarding',
  [ONBOARDING_STATES.MARK_INVITE_MEMBER_SKIPPED]: '/buyer/finalize-onboarding',
  [ONBOARDING_STATES.MARK_ONBOARDING_COMPLETED]: '/buyer/dashboard',
};

const SUPPLIER_ROUTES: Record<string, string> = {
  [ONBOARDING_STATES.MARK_BUSINESS_DETAILS_FILLED]: '/supplier/kyc',
  [ONBOARDING_STATES.MARK_USER_KYC_FILLED]: '/supplier/invite-team',
  [ONBOARDING_STATES.MARK_MEMBER_INVITED]: '/supplier/finalize-onboarding',
  [ONBOARDING_STATES.MARK_INVITE_MEMBER_SKIPPED]: '/supplier/finalize-onboarding',
  [ONBOARDING_STATES.MARK_ONBOARDING_COMPLETED]: '/supplier/dashboard',
};

const DEFAULT_ROUTE = {
  [UserTypeEnum.BUYER]: '/buyer/jurisdiction',
  [UserTypeEnum.SUPPLIER]: '/supplier/jurisdiction',
};

export function useOnboardingStatusRedirect() {
  const router = useRouter();
  const pathname = usePathname();
  const { data, isFetching, refetch } = useGetMe();
  const { updateUserData } = userStore();
  useEffect(() => {
    if (isFetching || !data) return;

    const { onboarding_state, user_type } = data;
    const userTypeLabel = userTypeLabelMap[user_type as UserTypeEnum];

    updateUserData({
      ...data,
      ...(userTypeLabel ? { userTypeLabel } : {}),
    });

    let nextRoute = '';

    if (user_type === UserTypeEnum.BUYER) {
      nextRoute = BUYER_ROUTES[onboarding_state] || DEFAULT_ROUTE[UserTypeEnum.BUYER];
    } else if (user_type === UserTypeEnum.SUPPLIER) {
      nextRoute = SUPPLIER_ROUTES[onboarding_state] || DEFAULT_ROUTE[UserTypeEnum.SUPPLIER];
    } else {
      console.warn('Unknown user_type:', user_type);
      return;
    }

    // Avoid unnecessary redirects
    if (pathname !== nextRoute) {
      router.replace(nextRoute);
    }
  }, [data, isFetching, router, pathname, updateUserData]);

  return { data, isFetching, refetch };
}
