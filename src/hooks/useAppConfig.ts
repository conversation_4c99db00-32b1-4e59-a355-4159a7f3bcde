import { useConfigStore } from '@/store/useAppConfig.store';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

const useAppConfig = () => {
  const { setConfig, setLoading, setError } = useConfigStore();

  return useQuery({
    queryKey: ['appConfig'],
    queryFn: async () => {
      try {
        setLoading(true);
        const response = await axios.get('/appConfig.json');
        setConfig(response.data);
        return response.data;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load app config';
        setError(errorMessage);
        throw error;
      }
    },
    staleTime: Infinity,
    gcTime: Infinity,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export default useAppConfig;
