import { useMemo } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createZodSchema, getDefaultValues } from '@/utils/formio/schema-generator';
import { FormioSchema } from '@/utils/formio';

type UseDynamicZodFormOptions = {
  formioJson: FormioSchema;
  mode?: 'onSubmit' | 'onBlur' | 'onChange' | 'all';
};

export function useDynamicZodForm({
  formioJson,
  mode = 'onSubmit',
}: UseDynamicZodFormOptions): UseFormReturn<any> {
  const schema = useMemo(() => createZodSchema(formioJson.components), [formioJson.components]);

  const defaultValues = useMemo(
    () => getDefaultValues(formioJson.components),
    [formioJson.components],
  );

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues,
    mode,
  });

  return form;
}
