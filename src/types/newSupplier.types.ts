import { UserTypeEnum } from '@/constants/user';

/* eslint-disable @typescript-eslint/no-explicit-any */
export type PhoneNo = string;
export type Country = {
  id: number;
  name: string;
  iso2: string;
  iso3: string;
  alpha2: string;
  alpha3: string;
  emoji?: string;
  ioc: string;
  countryCallingCodes: string[];
};
export type Jurisdiction = {
  countryOfOrigin: Country;
  countriesToServe: Country[];
};
export type OrgType = { id: number; name: string };
export type AccountBasics = {
  [key: string]: any;
};

export type SupplierOnboardingData = {
  phoneNo: PhoneNo;
  jurisdiction: Jurisdiction;
  orgType: OrgType[];
  accountBasics: AccountBasics;
  isEmailVerified?: boolean;
  role: UserTypeEnum;
};
