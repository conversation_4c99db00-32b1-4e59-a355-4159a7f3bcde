import { OrgType } from '@/components/buyer/onboarding/finalize/types';

export interface OrganisationType {
  id: number;
  name: OrgType;
}

export interface Jurisdiction {
  country: Country;
  state: State;
}

export interface Country {
  id: number;
  name: string;
}

export interface State {
  id: number;
  name: string;
}

export interface BuyerDetails {
  email: string;
  phone: string;
  tos_accept: boolean;
  display_name: string;
}

export interface FinalizeInfoResponse {
  phone: string;
  email: string;
  organisation_type: OrganisationType;
  jurisdiction: Jurisdiction;
  buyer_details: BuyerDetails;
  pending_request: boolean;
}
