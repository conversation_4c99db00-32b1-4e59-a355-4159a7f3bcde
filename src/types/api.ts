/* eslint-disable @typescript-eslint/no-explicit-any */
import { AxiosError } from 'axios';

// Base response interface
export interface APIResponse<T = any> {
  data?: T;
  statusCode?: number;
  headers?: any;
  success: boolean;
  error?: any;
  message?: string;
}

// Pagination interfaces
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Common API error interface
export interface APIError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
}

// Search and filter interfaces
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
}

// File upload interfaces
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

// Common entity interfaces
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// Status types
export type EntityStatus = 'active' | 'inactive' | 'pending' | 'deleted';

// API method types
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request configuration
export interface RequestConfig {
  timeout?: number;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  withCredentials?: boolean;
}

// Utility type for making all properties optional (for updates)
export type PartialUpdate<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;

// Utility type for creating new entities (without id, createdAt, updatedAt)
export type CreateEntity<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;

// Common query options
export interface QueryOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retry?: number | boolean;
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
}

// Mutation options
export interface MutationOptions {
  onSuccess?: (data: any, variables: any) => void;
  onError?: (error: any, variables: any) => void;
  onSettled?: (data: any, error: any, variables: any) => void;
}

export type APIClientError = {
  success: false;
  message: string;
  originalError: any;
};

export interface UseMutationHookProps<S, E, T = unknown> {
  onSuccess?: (data: S) => void;
  onError?: (error: AxiosError<E>) => void;
  onSettled?: (data: T) => void;
}
