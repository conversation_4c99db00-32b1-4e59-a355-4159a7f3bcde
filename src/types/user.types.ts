import { OnboardingState } from '@/constants/onboarding-status';

export type UserTypeLabel = 'buyer' | 'supplier' | 'admin';

export interface User extends UserMeResponse {
  userTypeLabel: UserTypeLabel;
  isEmailVerified: boolean;
}

export interface UserMeResponse {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: number; // probably should be something admin or manager or employee etc
  onboarding_state: OnboardingState;
  user_type: number; // 1 or 2 or 3
  tenant: Tenant;
  // TODO: discuss with BE and add organisationType and sourcing from and sourcing to data
}

export interface Tenant {
  id: number;
  name: string;
  kyc_status: number;
}

export interface UpdateUserRolePayload {
  id: number;
  role: string;
}
