import { UserTypeEnum } from '@/constants/user';

export type PhoneNo = string;
export type Country = {
  id: number;
  name: string;
  iso2: string;
  iso3: string;
  alpha2: string;
  alpha3: string;
  emoji?: string;
  ioc: string;
  countryCallingCodes: string[];
};

export type State = { id: number; name: string; state_code: string; iso2: string };

export type Jurisdiction = {
  country: Country;
  state: State;
};
export type OrgType = { id: number; name: string };

export type AccountBasics = {
  [key: string]: unknown;
};

export type BuyerOnboardingData = {
  phoneNo: PhoneNo;
  jurisdiction: Jurisdiction;
  orgType: OrgType;
  accountBasics: AccountBasics;
  isEmailVerified?: boolean;
  role: UserTypeEnum;
};

export interface BuyerInfoResponse {
  phone: string;
  email: string;
  organisation_type: OrgType;
  jurisdiction: Jurisdiction;
  buyer_details: {
    [key: string]: any;
  };
}

export interface UpdateBuyerInfoPayload {
  name: string;
  organisation_type: number;
  jurisdiction: number;
  buyer_details: {
    [key: string]: any;
  };
}
