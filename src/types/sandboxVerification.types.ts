export interface PanVerifyPayload {
  full_name: string;
  pan_number: string;
  dob: string;
}

export interface PanVerifyResponse {
  status: number; // 0 for success
  pan_holder: string;
  pan_number: string;
  dob: string;
  message?: string;
}

export interface SendAadhaarPayload {
  aadhaar_number: string;
}

export interface SendAadhaarResponse {
  status: number;
  reference_id: string;
  transaction_id: string;
  message: string;
}

export interface VerifyAadhaarOtpPayload {
  otp: string;
}

export interface VerifyAadhaarOtpResponse {
  status: number; //0 is success
  data: string;
  message: string;
}

export interface GstinLookupPayload {
  gstin: string;
}

export interface GstinLookupResponse {
  gstin: string;
  legal_name: string;
  reg_address: string;
  gst_status: string;
  constitution: string;
  manual_review_required: boolean;
  status: number;
}

export interface SendAadhaarPayload {
  aadhaar_number: string;
}

export interface SendAadhaarResponse {
  status: number;
  reference_id: string;
  transaction_id: string;
  message: string;
}

export interface VerifyAadhaarOtpPayload {
  otp: string;
}

export interface VerifyAadhaarOtpResponse {
  status: number; //0 is success
  data: string;
  message: string;
}
